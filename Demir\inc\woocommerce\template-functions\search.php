<?php
/**
 * Search specific template functions and hooks
 *
 * @package demir
 *
 * Function Index:
 * - demir_product_search() - Displays product search form based on theme settings
 * - DEMIR_AJAX class - Handles AJAX search functionality
 */

declare(strict_types=1);

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/**
 * AJAX Handler Class
 * -----------------
 */
if ( ! class_exists( 'DEMIR_AJAX' ) ) {
    class DEMIR_AJAX {

        /**
         * Initialize AJAX functionality
         */
        public static function init() {
            add_action( 'init', array( __CLASS__, 'define_ajax' ), 0 );
            add_action( 'template_redirect', array( __CLASS__, 'do_demir_ajax' ), 0 );
        }

        /**
         * Get AJAX endpoint URL
         */
        public static function get_endpoint( $request = '' ) {
            if ( class_exists( 'WC_AJAX' ) ) {
                return str_ireplace( 'wc-ajax=endpoint', 'demir-ajax', WC_AJAX::get_endpoint( 'endpoint' ) );
            } else {
                return esc_url_raw(
                    apply_filters(
                        'demir_ajax_get_endpoint',
                        add_query_arg(
                            'demir-ajax',
                            $request,
                            remove_query_arg( array( '_wpnonce' ), home_url( '/', 'relative' ) )
                        ),
                        $request
                    )
                );
            }
        }

        /**
         * Define AJAX constants
         */
        public static function define_ajax() {
            if ( ! empty( $_GET['demir-ajax'] ) ) {
                if ( ! defined( 'DOING_AJAX' ) ) {
                    define( 'DOING_AJAX', true );
                }
                if ( ! defined( 'WC_DOING_AJAX' ) ) {
                    define( 'WC_DOING_AJAX', true );
                }
                $GLOBALS['wpdb']->hide_errors();
            }
        }

        /**
         * Handle AJAX requests
         */
        public static function do_demir_ajax() {
            global $wp_query;

            if ( ! empty( $_GET['demir-ajax'] ) ) {
                $wp_query->set( 'demir-ajax', sanitize_text_field( wp_unslash( $_GET['demir-ajax'] ) ) );
            }

            $action = $wp_query->get( 'demir-ajax' );
            if ( $action ) {
                do_action( 'wp_ajax_' . $action );
                do_action( 'wp_ajax_nopriv_' . $action );
                wp_die();
            }
        }
    }

    // Initialize AJAX handler
    DEMIR_AJAX::init();
}

/**
 * AJAX Search Module
 * -----------------
 */

// Register AJAX search actions
add_action( 'wp_ajax_demir_ajax_search', 'demir_prepare_ajax_search' );
add_action( 'wp_ajax_nopriv_demir_ajax_search', 'demir_prepare_ajax_search' );

/**
 * Prepare AJAX search with nonce verification
 */
function demir_prepare_ajax_search() {
    $demir_nonce = isset( $_GET['demir_nonce'] ) ? sanitize_text_field( wp_unslash( $_GET['demir_nonce'] ) ) : '';

    if ( ! $demir_nonce || ! wp_verify_nonce( $demir_nonce, 'demir_nonce' ) ) {
        wp_send_json([
            'suggestions'   => [],
            'view_all_link' => '',
            'result_total'  => 0,
        ]);
    }

    demir_ajax_do_search();
}

/**
 * Perform AJAX search
 */
function demir_ajax_do_search() {
    $search_query = isset( $_GET['query'] ) ? sanitize_text_field( wp_unslash( $_GET['query'] ) ) : '';

    if ( empty( $search_query ) || strlen( $search_query ) < 2 ) {
        wp_send_json([
            'suggestions'   => [],
            'view_all_link' => '',
            'result_total'  => 0,
        ]);
    }

    // WooCommerce product search
    $args = array(
        'post_type'      => 'product',
        'post_status'    => 'publish',
        'posts_per_page' => 8,
        's'              => $search_query,
        'meta_query'     => array(
            'relation' => 'AND',
            array(
                'key'     => '_stock_status',
                'value'   => 'instock',
                'compare' => '='
            )
        ),
        'tax_query' => array(
            array(
                'taxonomy' => 'product_visibility',
                'field'    => 'name',
                'terms'    => array( 'exclude-from-catalog', 'exclude-from-search' ),
                'operator' => 'NOT IN',
            ),
        ),
    );

    $search_results = new WP_Query( $args );
    $suggestions = array();

    if ( $search_results->have_posts() ) {
        while ( $search_results->have_posts() ) {
            $search_results->the_post();

            $product = wc_get_product( get_the_ID() );
            if ( ! $product ) continue;

            $suggestions[] = array(
                'id'          => get_the_ID(),
                'title'       => get_the_title(),
                'url'         => get_permalink(),
                'image'       => get_the_post_thumbnail_url( get_the_ID(), 'thumbnail' ),
                'price'       => $product->get_price_html(),
                'description' => wp_trim_words( get_the_excerpt(), 15 ),
            );
        }
        wp_reset_postdata();
    }

    // Get total results for "view all" link
    $total_args = $args;
    $total_args['posts_per_page'] = -1;
    $total_args['fields'] = 'ids';
    $total_query = new WP_Query( $total_args );
    $total_results = $total_query->found_posts;

    // Create view all link
    $view_all_link = add_query_arg( 's', $search_query, wc_get_page_permalink( 'shop' ) );

    wp_send_json([
        'suggestions'   => $suggestions,
        'view_all_link' => $view_all_link,
        'result_total'  => $total_results,
        'query'         => $search_query,
    ]);
}

/**
 * Search Form Functions
 * --------------------
 */

if ( ! function_exists( 'demir_product_search' ) ) {
	/**
	 * Display Product Search form based on theme settings.
	 * Supports multiple search types including default WooCommerce search,
	 * AJAX Search for WooCommerce, Advanced Woo Search, Smart Search Pro,
	 * YITH WooCommerce AJAX Search, and built-in AJAX search.
	 *
	 * @since 1.0.0
	 * @return void
	 */
	function demir_product_search(): void {
		// Get theme options
		$search_display = demir_get_option( 'demir_layout_search_display' );
		$search_display_type = demir_get_option( 'demir_layout_search_display_type' );

		// Early return if WooCommerce is not active
		if ( ! demir_is_woocommerce_activated() ) {
			return;
		}

		// Determine search wrapper class based on display type
		$wrapper_class = 'outline' === $search_display_type ? 'site-search type-outline' : 'site-search';

		// Display appropriate search form based on settings
		switch ( $search_display ) {
			case 'enable':
				printf(
					'<div class="%s" role="search" aria-label="%s">',
					esc_attr($wrapper_class),
					esc_attr__('Product Search', 'demir')
				);
				the_widget( 'WC_Widget_Product_Search', 'title=' );
				echo '</div>';
				break;

			case 'demir-ajax':
				printf(
					'<div class="%s demir-ajax-search" role="search" aria-label="%s">',
					esc_attr($wrapper_class),
					esc_attr__('Product Search', 'demir')
				);
				demir_ajax_search_form();
				echo '</div>';
				break;

			case 'ajax-search-wc':
				printf(
					'<div class="%s" role="search" aria-label="%s">',
					esc_attr($wrapper_class),
					esc_attr__('Product Search', 'demir')
				);
				echo do_shortcode( '[fibosearch]' );
				echo '</div>';
				break;

			case 'advanced-woo-search':
				printf(
					'<div class="%s" role="search" aria-label="%s">',
					esc_attr($wrapper_class),
					esc_attr__('Product Search', 'demir')
				);
				echo do_shortcode( '[aws_search_form]' );
				echo '</div>';
				break;

			case 'smart-search-pro':
				printf(
					'<div class="%s" role="search" aria-label="%s">',
					esc_attr($wrapper_class),
					esc_attr__('Product Search', 'demir')
				);
				echo do_shortcode( '[smart_search id="1"]' );
				echo '</div>';
				break;

			case 'yith-search':
				printf(
					'<div class="%s" role="search" aria-label="%s">',
					esc_attr($wrapper_class),
					esc_attr__('Product Search', 'demir')
				);
				echo do_shortcode( '[yith_woocommerce_ajax_search]' );
				echo '</div>';
				break;

			case 'regular':
				printf(
					'<div class="%s" role="search" aria-label="%s">',
					esc_attr($wrapper_class),
					esc_attr__('Product Search', 'demir')
				);
				get_search_form();
				echo '</div>';
				break;
		}
	}
}

/**
 * Display AJAX search form
 */
if ( ! function_exists( 'demir_ajax_search_form' ) ) {
	function demir_ajax_search_form() {
		$placeholder = esc_attr__( 'Search products...', 'demir' );
		$nonce = wp_create_nonce( 'demir_nonce' );

		echo '<form role="search" method="get" class="demir-ajax-search-form" action="' . esc_url( wc_get_page_permalink( 'shop' ) ) . '">';
		echo '<div class="search-field-wrapper">';
		echo '<input type="search" class="search-field" placeholder="' . $placeholder . '" value="' . get_search_query() . '" name="s" autocomplete="off" />';
		echo '<button type="submit" class="search-submit" aria-label="' . esc_attr__( 'Search', 'demir' ) . '">';
		echo '<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">';
		echo '<path d="M7.333 12.667A5.333 5.333 0 1 0 7.333 2a5.333 5.333 0 0 0 0 10.667ZM14 14l-2.9-2.9" stroke="currentColor" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"/>';
		echo '</svg>';
		echo '</button>';
		echo '</div>';
		echo '<div class="demir-search-results" style="display: none;"></div>';
		echo '<input type="hidden" name="post_type" value="product" />';
		echo '<input type="hidden" name="demir_nonce" value="' . $nonce . '" />';
		echo '</form>';
	}
}



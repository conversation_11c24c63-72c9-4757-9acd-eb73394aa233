<?php
/**
 * Getting started template
 *
 * @package CommerceGurus
 * @subpackage demir
 */

$customizer_url = admin_url() . 'customize.php';
?>

<div id="usefulplugins" class="ccfw-tab-pane">

	<div class="ccfw-tab-pane-center">

		<h1 class="ccfw-welcome-title"><?php esc_html_e( 'Useful Plugins', 'demir' ); ?></h1>
		<h2><?php esc_html_e( 'Enhance your store with these useful optional plugin suggestions for demir. Just search the "Plugins" section of WordPress for the name, then install and activate. You will need to consult the plugin documentation of each for setup instructions.', 'demir' ); ?></h2>
		<br/>
		<table class="useful-table">
			<tbody>
				<tr>
					<td>
						<h3><?php esc_html_e( 'Autoptimize', 'demir' ); ?></h3>
						<p><?php esc_html_e( 'Optimizes your website, concatenating the CSS and JavaScript code, and compresses it.', 'demir' ); ?></p>
					</td>
					<td class="link">
						<a class="button-primary" target="_blank" href="<?php echo esc_url( 'https://wordpress.org/plugins/autoptimize/' ); ?>"><?php esc_html_e( 'More information', 'demir' ); ?></a>
					</td>
				</tr>
				<tr>
					<td>
						<h3><?php esc_html_e( 'Loco Translate', 'demir' ); ?></h3>
						<p><?php esc_html_e( 'Loco Translate provides in-browser editing of WordPress translation files. It is the easiest way to change your store language to something else.', 'demir' ); ?></p>
					</td>
					<td class="link">
						<a class="button-primary" target="_blank" href="<?php echo esc_url( 'https://wordpress.org/plugins/loco-translate/' ); ?>"><?php esc_html_e( 'More information', 'demir' ); ?></a>
					</td>
				</tr>
				<tr>
					<td>
						<h3><?php esc_html_e( 'MailChimp for WordPress', 'demir' ); ?></h3>
						<p><?php esc_html_e( 'Allows visitors to subscribe to your newsletters easily. Requires a free MailChimp account.', 'demir' ); ?></p>
					</td>
					<td class="link">
						<a class="button-primary" target="_blank" href="<?php echo esc_url( 'https://wordpress.org/plugins/mailchimp-for-wp/' ); ?>"><?php esc_html_e( 'More information', 'demir' ); ?></a>
					</td>
				</tr>
				<tr>
					<td>
						<h3><?php esc_html_e( 'Real-Time Find and Replace', 'demir' ); ?></h3>
						<p><?php esc_html_e( 'This plugin allows you to dynamically replace text with alternative copy of your choosing before a page is delivered to a user’s browser.', 'demir' ); ?></p>
					</td>
					<td class="link">
						<a class="button-primary" target="_blank" href="<?php echo esc_url( 'https://wordpress.org/plugins/real-time-find-and-replace/' ); ?>"><?php esc_html_e( 'More information', 'demir' ); ?></a>
					</td>
				</tr>
				<tr>
					<td>
						<h3><?php esc_html_e( 'WooCommerce Product Tabs', 'demir' ); ?></h3>
						<p><?php esc_html_e( 'Helps you add your own custom tabs to the product page in WooCommerce.', 'demir' ); ?></p>
					</td>
					<td class="link">
						<a class="button-primary" target="_blank" href="<?php echo esc_url( 'https://wordpress.org/plugins/woocommerce-product-tabs/' ); ?>"><?php esc_html_e( 'More information', 'demir' ); ?></a>
					</td>
				</tr>
				<tr>
					<td>
						<h3><?php esc_html_e( 'Weglot', 'demir' ); ?></h3>
						<p><?php esc_html_e( 'The best and easiest translation solution to translate your demir store and go multilingual.', 'demir' ); ?></p>
					</td>
					<td class="link">
						<a class="button-primary" target="_blank" href="<?php echo esc_url( 'https://wordpress.org/plugins/weglot/' ); ?>"><?php esc_html_e( 'More information', 'demir' ); ?></a>
					</td>
				</tr>
				<tr>
					<td>
						<h3><?php esc_html_e( 'WPForms', 'demir' ); ?></h3>
						<p><?php esc_html_e( 'WPForms allows you to create contact forms, and any other kind of form on your site in minutes.', 'demir' ); ?></p>
					</td>
					<td class="link">
						<a class="button-primary" target="_blank" href="<?php echo esc_url( 'https://wordpress.org/plugins/wpforms-lite/' ); ?>"><?php esc_html_e( 'More information', 'demir' ); ?></a>
					</td>
				</tr>

			</tbody>
		</table>
	</div>
	<div class="ccfw-clear"></div>
</div>



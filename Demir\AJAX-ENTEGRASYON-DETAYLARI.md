# CommerceKit AJAX Özelliği ve Shoptimizer Entegra<PERSON><PERSON><PERSON>, CommerceKit eklentisinin AJAX arama özelliğinin nasıl çalıştığını ve Shoptimizer teması ile entegrasyonunun nasıl sağlandığını kod örnekleriyle detaylı olarak açıklar. Amaç, başka bir yapay zekanın bu yapıyı doğrudan bir temaya entegre edebilmesini sağlamaktır.

---

## 1. AJAX Altyapısı Nasıl Kuruluyor?

### 1.1. PHP: AJAX Endpoint Tanımı

`includes/class-commercekit-ajax.php` dosyasında, özel bir AJAX endpoint'i tanımlanır:

```php
class COMMERCEKIT_AJAX {
    public static function init() {
        add_action( 'init', array( __CLASS__, 'define_ajax' ), 0 );
        add_action( 'template_redirect', array( __CLASS__, 'do_commercekit_ajax' ), 0 );
    }
    public static function get_endpoint( $request = '' ) {
        if ( class_exists( 'WC_AJAX' ) ) {
            return str_ireplace( 'wc-ajax=endpoint', 'commercekit-ajax', WC_AJAX::get_endpoint( 'endpoint' ) );
        } else {
            return esc_url_raw( apply_filters( 'commercekit_ajax_get_endpoint', add_query_arg( 'commercekit-ajax', $request, remove_query_arg( array( '_wpnonce' ), home_url( '/', 'relative' ) ) ), $request ) );
        }
    }
    public static function define_ajax() {
        if ( ! empty( $_GET['commercekit-ajax'] ) ) {
            if ( ! defined( 'DOING_AJAX' ) ) define( 'DOING_AJAX', true );
            if ( ! defined( 'WC_DOING_AJAX' ) ) define( 'WC_DOING_AJAX', true );
            $GLOBALS['wpdb']->hide_errors();
        }
    }
    public static function do_commercekit_ajax() {
        global $wp_query;
        if ( ! empty( $_GET['commercekit-ajax'] ) ) {
            $wp_query->set( 'commercekit-ajax', sanitize_text_field( wp_unslash( $_GET['commercekit-ajax'] ) ) );
        }
        $action = $wp_query->get( 'commercekit-ajax' );
        if ( $action ) {
            do_action( 'wp_ajax_' . $action );
            wp_die();
        }
    }
}
COMMERCEKIT_AJAX::init();
```

Bu yapı sayesinde, `?commercekit-ajax=action_name` şeklinde özel AJAX endpoint'leri oluşturulur.

### 1.2. AJAX Arama İşlemi

`includes/module-ajax-search.php` ve `includes/module-fast-ajax-search.php` dosyalarında arama işlemleri yönetilir.

#### AJAX Arama Action'ı:
```php
add_action( 'wp_ajax_commercekit_ajax_search', 'commercekit_prepare_ajax_search' );
add_action( 'wp_ajax_nopriv_commercekit_ajax_search', 'commercekit_prepare_ajax_search' );

function commercekit_prepare_ajax_search() {
    $commercekit_nonce = isset( $_GET['commercekit_nonce'] ) ? sanitize_text_field( wp_unslash( $_GET['commercekit_nonce'] ) ) : '';
    if ( ! $commercekit_nonce || ! wp_verify_nonce( $commercekit_nonce, 'commercekit_nonce' ) ) {
        wp_send_json([
            'suggestions'   => [],
            'view_all_link' => '',
            'result_total'  => 0,
        ]);
    }
    commercekit_ajax_do_search();
}
```

#### Hızlı Arama (Fast Search):
`cgkit-search-api.php` dosyası, WordPress çekirdeğini hızlıca başlatıp arama işlemini yapar:

```php
if ( isset( $options['ajs_fast_search'] ) && 1 === (int) $options['ajs_fast_search'] ) {
    require_once dirname( __FILE__ ) . '/includes/module-fast-ajax-search.php';
    commercekit_ajax_do_search( $search_type );
}
```

---

## 2. Frontend (JS) ile AJAX Arama

`assets/js/ajax-search.js` dosyasında, arama input'una yazılan karakterlere göre AJAX ile arama tetiklenir:

```js
if ( commercekit_ajs.ajax_search == 1 ) {
    function ckit_ajax_search(){
        var minChar = commercekit_ajs.char_count;
        var inputs = document.querySelectorAll('input[type="search"]');
        inputs.forEach(function(input){
            input.addEventListener('keyup', function(e){
                if(input.value.length >= minChar){
                    var url = commercekit_ajs.ajax_url + '=' + commercekit_ajs.action + '&query=' + encodeURIComponent(input.value) + '&commercekit_nonce=' + ajax_nonce;
                    fetch(url).then(response => response.json()).then(json => {
                        // Sonuçları input altında göster
                    });
                }
            });
        });
    }
    ckit_ajax_search();
}
```

---

## 3. Shoptimizer Teması ile Entegrasyon

### 3.1. Tema Algılama ve Özelleştirme
- Eklenti, Shoptimizer teması aktifse bazı fonksiyonları ve CSS'leri özel olarak uygular.
- Örneğin, sticky add to cart çubuğu için Shoptimizer'ın kendi fonksiyonu devre dışı bırakılır:

```php
// includes/module-sticky-atc-bar.php
action( 'woocommerce_before_single_product', 'commercekit_sticky_atc_bar_remove_shoptimizer_atc', 10 );
function commercekit_sticky_atc_bar_remove_shoptimizer_atc() {
    remove_action( 'woocommerce_before_single_product_summary', 'shoptimizer_sticky_single_add_to_cart', 30 );
}
```

### 3.2. CSS ile Tema Uyumu
- CSS dosyalarında `.theme-shoptimizer` gibi class'lar ile Shoptimizer'a özel stiller uygulanır:

```css
/* assets/css/commercekit-sticky-atc.css */
.theme-shoptimizer.single-product .site-content .commercekit-sticky-add-to-cart .col-full {
    max-width: 1170px;
    margin-right: auto;
    margin-left: auto;
    background-color: #fff;
}
```

### 3.3. Admin Paneli ve Lisans
- Admin panelinde, Shoptimizer teması ve CommerceKit lisansı algılanır ve özel ayarlar/güncellemeler açılır:

```php
// includes/admin-settings.php
function commercekit_is_domain_connected() {
    $theme = wp_get_theme();
    $template = $theme->get_template();
    if ( false === stripos( $template, 'shoptimizer' ) ) {
        return false;
    }
    // ...
}
```

---

## 4. Özet Akış

1. **AJAX endpoint** tanımlanır ve WordPress'e eklenir.
2. **Frontend'de** arama input'una yazıldıkça JS ile AJAX istekleri yapılır.
3. **PHP tarafında** arama sonuçları hazırlanır ve JSON olarak döndürülür.
4. **Shoptimizer teması** algılandığında, eklenti kendi fonksiyonlarını ve stillerini uygular, temanın ilgili fonksiyonlarını devre dışı bırakır.

---

## 5. Entegre Etmek İçin Gerekenler

- Yukarıdaki PHP ve JS yapılarını temaya ekle.
- AJAX endpoint'ini ve arama action'larını tanımla.
- Shoptimizer'a özel CSS ve fonksiyonları uygula.
- Admin panelinde tema algılaması ve lisans kontrolü ekle.

---

Daha fazla detay veya örnek kod için dosya/detay belirtmeniz yeterli! 
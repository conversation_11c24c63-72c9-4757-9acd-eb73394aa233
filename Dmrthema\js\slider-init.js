document.addEventListener('DOMContentLoaded', function () {
    // WordPress'ten slider ayarlarını al
    const sliderSettings = window.dmrthemaSliderSettings || {};
    const autoplayEnabled = sliderSettings.autoplay === '1';
    const autoplayDelay = parseInt(sliderSettings.delay) || 5000;

    // Swiper konfigürasyonu
    const swiperConfig = {
        // Swiper ayarları
        loop: true,

        // İleri/geri butonları
        navigation: {
            nextEl: '.swiper-button-next',
            prevEl: '.swiper-button-prev',
        },

        // Sayfalama noktaları
        pagination: {
            el: '.swiper-pagination',
            clickable: true,
        },
    };

    // Otomatik geçiş ayarı varsa ekle
    if (autoplayEnabled) {
        swiperConfig.autoplay = {
            delay: autoplayDelay,
            disableOnInteraction: false, // Kullanıcı etkileşiminde durmasın
        };
    }

    const swiper = new Swiper('.main-slider', swiperConfig);
});

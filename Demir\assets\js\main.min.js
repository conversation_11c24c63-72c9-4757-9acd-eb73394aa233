var canRunClickFunc=!0;function makeTouchstartWithClick(e){if(!canRunClickFunc)return!1;setTimeout(function(){canRunClickFunc=!0},700);var t=e.target,n=t.closest(".close-drawer");if(t.classList.contains("close-drawer")||n){document.querySelector("body").classList.remove("filter-open"),document.querySelector("body").classList.remove("mobile-toggled");return}var n=t.closest(".menu-toggle");if(t.classList.contains("menu-toggle")||n){e.stopPropagation(),e.preventDefault(),document.querySelector("body").classList.add("mobile-toggled");return}if(t.classList.contains("mobile-overlay")){document.querySelector("body").classList.remove("filter-open"),document.querySelector("body").classList.remove("mobile-toggled");return}}function makeOnTouchTapped(){992<window.innerWidth&&"ontouchstart"in window&&(document.addEventListener("touchstart",function(){},!0),document.addEventListener("click",function(e){var t=e.target;if(!t.classList.contains("menu-item-has-children")){var n=t.closest(".menu-item");if(!n||!n.classList.contains("menu-item-has-children"))return!0;t=n}var r=document.querySelectorAll(".menu-item-has-children.tapped");return r&&r.forEach(function(e){e!==t&&e.classList.remove("tapped")}),!!t.classList.contains("tapped")||(t.classList.add("tapped"),e.preventDefault(),!1)},!0))}function handleFirstTab(e){9===e.keyCode&&(document.body.classList.add("keyboard-active"),window.removeEventListener("keydown",handleFirstTab))}function cartDrawerTrapTabKey(e){var t=document.querySelector("body.drawer-open #demirCartDrawer");if(t){if("escape"==e.key.toLowerCase()){document.querySelector("body").classList.remove("drawer-open");return}if("tab"==e.key.toLowerCase()){var r=["a[href]","area[href]",'input:not([disabled]):not([type="hidden"]):not([aria-hidden])',"select:not([disabled]):not([aria-hidden])","textarea:not([disabled]):not([aria-hidden])","button:not([disabled]):not([aria-hidden])","iframe","object","embed","[contenteditable]",'[tabindex]:not([tabindex^="-"])'],n=t.querySelectorAll(r),a=Array(...n);if(0===a.length)return;if(a=a.filter(e=>null!==e.offsetParent),t.contains(document.activeElement)){var o=a.indexOf(document.activeElement);e.shiftKey&&0===o&&(a[a.length-1].focus(),e.preventDefault()),!e.shiftKey&&a.length>0&&o===a.length-1&&(a[0].focus(),e.preventDefault())}else a[0].focus()}}}function updateMenuAriaExpanded(){var e=document.querySelectorAll("#menu-primary-menu li > .sub-menu-wrapper");e&&e.forEach(function(e){var t=e.closest("li.menu-item-has-children");t&&("hidden"!==window.getComputedStyle(e).visibility?t.hasAttribute("aria-expanded")&&t.setAttribute("aria-expanded","true"):t.hasAttribute("aria-expanded")&&t.setAttribute("aria-expanded","false"))})}document.addEventListener("DOMContentLoaded",function(){window.addEventListener("load",function(e){var t=.01*window.innerHeight;document.documentElement.style.setProperty("--vh",t+"px"),makeOnTouchTapped()}),window.addEventListener("resize",function(e){var t=.01*window.innerHeight;document.documentElement.style.setProperty("--vh",t+"px"),makeOnTouchTapped()}),window.addEventListener("click",function(e){var t=e.target,n=t.closest(".demir-mobile-filter");if(t.classList.contains("demir-mobile-toggle")||n){e.stopPropagation(),e.preventDefault(),document.querySelector("body").classList.toggle("filter-open");return}var n=t.closest(".mobile-search-toggle");if(t.classList.contains("mobile-search-toggle")||n){e.stopPropagation(),e.preventDefault(),document.querySelector("body").classList.toggle("m-search-toggled");return}makeTouchstartWithClick(e),canRunClickFunc=!1}),window.addEventListener("touchstart",function(e){makeTouchstartWithClick(e),canRunClickFunc=!1});var e=document.querySelector(".single-product form.cart");e&&e.setAttribute("id","sticky-scroll");var t=document.querySelectorAll(".button-wrapper");t&&t.forEach(function(e){e.classList.add("demir-size-guide")});var n=document.querySelectorAll("li.full-width");n&&n.forEach(function(e){e.addEventListener("mouseenter",function(e){var t=document.querySelector(".site");t&&t.classList.add("overlay")}),e.addEventListener("mouseleave",function(e){var t=document.querySelector(".site");t&&t.classList.remove("overlay")})});var r=document.querySelector(".col-full-nav");r&&r.closest(".mobile-toggled")&&(r.addEventListener("click",function(e){document.querySelector("body").classList.remove("mobile-toggled")}),r.addEventListener("touchstart",function(e){document.querySelector("body").classList.remove("mobile-toggled")}));var o=document.querySelectorAll("body .main-navigation ul.menu li.menu-item-has-children .caret");o&&o.forEach(function(e){e.addEventListener("click",function(e){e.target.closest("li").classList.toggle("dropdown-open"),e.preventDefault()})});var i=document.querySelectorAll(".main-navigation ul.menu li.menu-item-has-children > a");i&&i.forEach(function(e){"#"===e.getAttribute("href")&&e.addEventListener("click",function(e){e.target.closest("li").classList.toggle("dropdown-open"),e.preventDefault()})});var a=document.querySelector(".logo-mark a");a&&a.addEventListener("click",function(e){e.target,e.preventDefault(),window.scroll({behavior:"smooth",left:0,top:0})});var s=document.querySelectorAll("a.variable-grouped-sticky");s&&s.forEach(function(e){"#"!==e.getAttribute("href")&&e.addEventListener("click",function(t){var n=document.querySelector(e.getAttribute("href"));n&&(t.preventDefault(),window.scroll({behavior:"smooth",left:0,top:n.offsetTop-80}))})});var l=[].slice.call(document.querySelectorAll("img.lazy")),c=!1;let u=function(){!1===c&&(c=!0,setTimeout(function(){l.forEach(function(e){e.getBoundingClientRect().top<=window.innerHeight&&0<=e.getBoundingClientRect().bottom&&"none"!==getComputedStyle(e).display&&(e.src=e.dataset.src,e.dataset.srcset&&(e.srcset=e.dataset.srcset),e.classList.remove("lazy"),0===(l=l.filter(function(t){return t!==e})).length&&(document.removeEventListener("scroll",u),window.removeEventListener("resize",u),window.removeEventListener("orientationchange",u)))}),c=!1},200))};document.addEventListener("scroll",u),window.addEventListener("resize",u),window.addEventListener("orientationchange",u),jQuery&&jQuery("body").on("added_to_cart",function(e,t,n){document.querySelector("body").classList.remove("mobile-toggled")});var d=document.querySelectorAll("#menu-primary-menu li.menu-item-has-children");d&&d.forEach(function(e){e.addEventListener("mouseenter",function(){setTimeout(function(){updateMenuAriaExpanded()},50)}),e.addEventListener("mouseleave",function(){setTimeout(function(){updateMenuAriaExpanded()},50)})}),document.addEventListener("focusin",function(){setTimeout(function(){updateMenuAriaExpanded()},50)}),document.addEventListener("focusout",function(){setTimeout(function(){updateMenuAriaExpanded()},50)}),document.addEventListener("keydown",function(e){cartDrawerTrapTabKey(e)})}),window.addEventListener("keydown",handleFirstTab);

/**
 * Mega Menu JavaScript for DmrThema
 * Fare ile mega menu uzerinde gezinirken menunun kapanmasini onler
 */

document.addEventListener('DOMContentLoaded', function() {

    // Mega menu elementlerini sec
    const megaMenuItems = document.querySelectorAll('.main-navigation ul li.has-mega-menu');

    if (megaMenuItems.length === 0) {
        return; // Mega menu yoksa cik
    }
    
    // Her mega menu item icin event listener'lar ekle
    megaMenuItems.forEach(function(menuItem) {
        const subMenu = menuItem.querySelector('.sub-menu');

        if (!subMenu) {
            return; // Alt menu yoksa devam et
        }

        let hoverTimeout;

        // Menu item uzerine fare geldiginde
        menuItem.addEventListener('mouseenter', function() {
            // Timeout'u temizle (eger varsa)
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }

            // Diger acik mega menuleri kapat
            closeMegaMenus();

            // Bu mega menuyu ac - animasyon icin kisa gecikme
            requestAnimationFrame(function() {
                menuItem.classList.add('mega-menu-active');
                // 4 sütunlu mega menu için hover efektlerini başlat
                initializeMegaMenuHover(menuItem);
            });
        });
        
        // Menu item'dan fare ciktiginda
        menuItem.addEventListener('mouseleave', function() {
            // Kisa bir gecikme ile menu kapat
            // Bu gecikme fareyi mega menu uzerine tasima firsati verir
            hoverTimeout = setTimeout(function() {
                menuItem.classList.remove('mega-menu-active');
            }, 150); // 150ms gecikme
        });
        
        // Sub menu uzerine fare geldiginde
        subMenu.addEventListener('mouseenter', function() {
            // Timeout'u temizle - menu acik kalsin
            if (hoverTimeout) {
                clearTimeout(hoverTimeout);
                hoverTimeout = null;
            }
            
            // Menu acik kalsin
            menuItem.classList.add('mega-menu-active');
        });
        
        // Sub menu'dan fare ciktiginda
        subMenu.addEventListener('mouseleave', function() {
            // Menu kapat
            menuItem.classList.remove('mega-menu-active');
        });
    });
    
    // Tum mega menuleri kapat
    function closeMegaMenus() {
        megaMenuItems.forEach(function(item) {
            item.classList.remove('mega-menu-active');
        });
    }
    
    // Sayfa uzerinde baska bir yere tiklandiginda mega menuleri kapat
    document.addEventListener('click', function(e) {
        // Tiklanilan element mega menu icinde degilse
        let isInsideMegaMenu = false;
        
        megaMenuItems.forEach(function(menuItem) {
            if (menuItem.contains(e.target)) {
                isInsideMegaMenu = true;
            }
        });
        
        if (!isInsideMegaMenu) {
            closeMegaMenus();
        }
    });
    
    // ESC tusuna basildiginda mega menuleri kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMegaMenus();
        }
    });

    // 4 sütunlu mega menu hover efektlerini başlat
    function initializeMegaMenuHover(menuItem) {
        const subMenuContainer = menuItem.querySelector('.sub-menu-container');
        if (!subMenuContainer) return;

        // Bloklu mega menu kontrolü - sayfa içeriği varsa grid yapısını uygulama
        const hasPageContent = subMenuContainer.querySelector('.mega-menu-page-content');
        if (hasPageContent) {
            // Bloklu mega menu - hiçbir şey yapma, mevcut yapıyı koru
            return;
        }

        // Mobil kontrolü
        const isMobile = window.innerWidth <= 768;
        const isTablet = window.innerWidth <= 1024 && window.innerWidth > 768;

        // Sadece bloksuz mega menüler için grid yapıya dönüştür
        convertToFourColumnLayout(subMenuContainer);

        // Mobilde farklı davranış
        if (isMobile) {
            // Mobilde tüm sütunları göster, hover efekti yok
            showAllColumnsForMobile(subMenuContainer);
            return;
        }

        // İlk sütundaki ana menü öğelerine hover efekti ekle
        const column1 = subMenuContainer.querySelector('.mega-menu-column-1');
        if (!column1) return;

        const mainMenuItems = column1.querySelectorAll('li > a');

        mainMenuItems.forEach(function(mainItem, index) {
            // Hover efekti için debounce
            let hoverTimer;

            mainItem.addEventListener('mouseenter', function() {
                // Mobil kontrolü tekrar yap
                if (window.innerWidth <= 768) return;

                // Önceki timer'ı temizle
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                }

                // Kısa gecikme ile hover efektini uygula
                hoverTimer = setTimeout(() => {
                    // Tüm ana menü öğelerinden active sınıfını kaldır
                    mainMenuItems.forEach(item => item.classList.remove('active'));

                    // Bu öğeye active sınıfı ekle
                    this.classList.add('active');

                    // 2-4. sütunları gizle
                    hideAllSubColumns(subMenuContainer);

                    // Alt kategorileri göster
                    showSubCategoriesForItem(subMenuContainer, this, index, isTablet);
                }, 100); // 100ms gecikme
            });

            // Mouse leave olayı
            mainItem.addEventListener('mouseleave', function() {
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                }
            });
        });

        // Varsayılan olarak hiçbir öğe aktif olmasın - kullanıcı hover yapana kadar bekle

        // 2. sütundaki menü öğelerine hover efekti ekle (3. seviye için)
        setupThirdLevelMenus(subMenuContainer, isTablet);

        // 3. sütundaki menü öğelerine hover efekti ekle (4. seviye için)
        setupFourthLevelMenus(subMenuContainer, isTablet);
    }

    // Mevcut yapıyı 4 sütunlu yapıya dönüştür
    function convertToFourColumnLayout(container) {
        // Eğer zaten dönüştürülmüşse tekrar yapma
        if (container.querySelector('.mega-menu-column-1')) return;

        // Mevcut li öğelerini al
        const existingItems = Array.from(container.children);

        // Container'ı temizle
        container.innerHTML = '';

        // 4 sütun oluştur
        const column1 = document.createElement('div');
        column1.className = 'mega-menu-column-1';

        const column2 = document.createElement('div');
        column2.className = 'mega-menu-column-2';

        const column3 = document.createElement('div');
        column3.className = 'mega-menu-column-3';

        const column4 = document.createElement('div');
        column4.className = 'mega-menu-column-4';

        // Mevcut öğeleri ilk sütuna taşı ve alt menüye sahip olanları işaretle
        existingItems.forEach(item => {
            column1.appendChild(item);

            // Alt menüye sahip öğeleri kontrol et ve has-submenu sınıfını ekle
            const link = item.querySelector('a');
            const subMenu = item.querySelector('ul');
            if (link && subMenu && subMenu.children.length > 0) {
                link.classList.add('has-submenu');
            }
        });

        // Sütunları container'a ekle
        container.appendChild(column1);
        container.appendChild(column2);
        container.appendChild(column3);
        container.appendChild(column4);
    }

    // Alt menüye sahip öğeleri işaretle
    function markSubmenuItems(container) {
        // Tüm sütunlardaki linkleri kontrol et
        const allLinks = container.querySelectorAll('a');

        allLinks.forEach(link => {
            const parentLi = link.closest('li');
            if (parentLi) {
                const subMenu = parentLi.querySelector('ul');
                if (subMenu && subMenu.children.length > 0) {
                    link.classList.add('has-submenu');
                }
                // has-submenu sınıfını kaldırma - bir kez eklendikten sonra korunmalı
                // else {
                //     link.classList.remove('has-submenu');
                // }
            }
        });
    }

    // Tüm alt sütunları gizle
    function hideAllSubColumns(container) {
        const columns = container.querySelectorAll('.mega-menu-column-2, .mega-menu-column-3, .mega-menu-column-4');
        columns.forEach(column => {
            column.classList.remove('active');
            column.style.display = 'none';
            column.style.opacity = '0';
            column.innerHTML = '';
        });
    }

    // Belirli bir menü öğesi için alt kategorileri göster
    function showSubCategoriesForItem(container, menuItem, index, isTablet = false) {
        const parentLi = menuItem.closest('li');
        const subMenuUl = parentLi.querySelector('ul');

        console.log('Seçilen menü:', menuItem.textContent.trim());

        if (!subMenuUl) {
            console.log('Bu menünün alt öğesi yok');
            return;
        }

        // 2-4. sütunları gizle
        hideAllSubColumns(container);

        const column2 = container.querySelector('.mega-menu-column-2');
        if (!column2) return;

        // Alt menü öğelerini al
        const subItems = Array.from(subMenuUl.children);
        console.log('Alt öğeler:', subItems.map(item => item.querySelector('a')?.textContent.trim()));

        // 2. sütunu temizle ve alt menü öğelerini ekle
        column2.innerHTML = '';
        const ul = document.createElement('ul');

        subItems.forEach(item => {
            const clonedItem = item.cloneNode(true);

            // Orijinal öğenin has-submenu sınıfını kontrol et ve koru
            const originalLink = item.querySelector('a');
            const clonedLink = clonedItem.querySelector('a');
            const originalSubMenu = item.querySelector('ul');

            if (originalLink && clonedLink && originalSubMenu && originalSubMenu.children.length > 0) {
                clonedLink.classList.add('has-submenu');
            }

            ul.appendChild(clonedItem);
        });

        column2.appendChild(ul);

        // 2. sütunu göster
        column2.style.display = 'block';
        column2.classList.add('active');

        setTimeout(() => {
            column2.style.opacity = '1';
        }, 10);
    }



    // Mobil için tüm sütunları göster
    function showAllColumnsForMobile(container) {
        const column1 = container.querySelector('.mega-menu-column-1');
        if (!column1) return;

        const mainMenuItems = column1.querySelectorAll('> li');

        mainMenuItems.forEach((mainItem, index) => {
            const subMenuUl = mainItem.querySelector('ul');
            if (!subMenuUl) return;

            // Alt menü öğelerini mobil için düzenle
            const subItems = Array.from(subMenuUl.children);
            const targetColumn = index % 3 === 0 ? '.mega-menu-column-2' :
                               index % 3 === 1 ? '.mega-menu-column-3' : '.mega-menu-column-4';

            const targetColumnEl = container.querySelector(targetColumn);
            if (targetColumnEl) {
                const ul = targetColumnEl.querySelector('ul') || document.createElement('ul');
                if (!targetColumnEl.querySelector('ul')) {
                    targetColumnEl.appendChild(ul);
                }

                // Ana menü başlığı ekle
                const titleLi = document.createElement('li');
                const titleA = document.createElement('a');
                titleA.textContent = mainItem.querySelector('> a').textContent;
                titleA.style.fontWeight = 'bold';
                titleA.style.color = '#333';
                titleA.style.borderBottom = '1px solid #e0e0e0';
                titleA.style.marginBottom = '10px';
                titleA.style.display = 'block';
                titleLi.appendChild(titleA);
                ul.appendChild(titleLi);

                // Alt öğeleri ekle
                subItems.forEach(item => {
                    ul.appendChild(item.cloneNode(true));
                });

                targetColumnEl.classList.add('active');
            }
        });
    }

    // 3. seviye menüler için hover efekti kurulumu
    function setupThirdLevelMenus(container, isTablet) {
        // 2. sütundaki menü öğelerine hover efekti ekle
        const column2 = container.querySelector('.mega-menu-column-2');
        if (!column2) return;

        // 2. sütun aktif olduğunda hover efektlerini kur
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.classList.contains('active') && target.classList.contains('mega-menu-column-2')) {
                        setupColumn2HoverEffects(container, isTablet);
                    }
                }
            });
        });

        observer.observe(column2, { attributes: true });
    }

    // 2. sütun hover efektlerini kur
    function setupColumn2HoverEffects(container, isTablet) {
        const column2 = container.querySelector('.mega-menu-column-2');
        if (!column2) return;

        const secondLevelItems = column2.querySelectorAll('li > a');

        secondLevelItems.forEach(function(secondItem, index) {
            let hoverTimer;

            secondItem.addEventListener('mouseenter', function() {
                // Mobil kontrolü
                if (window.innerWidth <= 768) return;

                // Önceki timer'ı temizle
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                }

                // Kısa gecikme ile hover efektini uygula
                hoverTimer = setTimeout(() => {
                    // 2. sütundaki tüm öğelerden active sınıfını kaldır
                    secondLevelItems.forEach(item => item.classList.remove('active'));

                    // Bu öğeye active sınıfı ekle
                    this.classList.add('active');

                    // 3. seviye alt kategorileri göster
                    showThirdLevelCategories(container, this, index, isTablet);
                }, 100);
            });

            secondItem.addEventListener('mouseleave', function() {
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                }
            });
        });
    }

    // 3. seviye kategorileri göster
    function showThirdLevelCategories(container, menuItem, index, isTablet) {
        // Sadece 4. sütunu gizle
        const column3 = container.querySelector('.mega-menu-column-3');
        const column4 = container.querySelector('.mega-menu-column-4');

        if (column4) {
            column4.classList.remove('active');
            column4.style.display = 'none';
            column4.style.opacity = '0';
        }

        // Bu menü öğesinin alt menüsünü bul
        const parentLi = menuItem.closest('li');
        const subMenuUl = parentLi ? parentLi.querySelector('ul') : null;

        if (!subMenuUl || subMenuUl.children.length === 0) return;

        // 3. sütunu göster ve içeriği doldur
        if (column3) {
            column3.innerHTML = '';

            const ul = document.createElement('ul');
            // Alt menü öğelerini 3. sütuna kopyala
            const subItems = Array.from(subMenuUl.children);
            subItems.forEach(subItem => {
                const clonedItem = subItem.cloneNode(true);

                // Orijinal öğenin has-submenu sınıfını kontrol et ve koru
                const originalLink = subItem.querySelector('a');
                const clonedLink = clonedItem.querySelector('a');
                const originalSubMenu = subItem.querySelector('ul');

                if (originalLink && clonedLink && originalSubMenu && originalSubMenu.children.length > 0) {
                    clonedLink.classList.add('has-submenu');
                }

                ul.appendChild(clonedItem);
            });

            column3.appendChild(ul);

            // 3. sütunu göster
            column3.style.display = 'block';
            column3.classList.add('active');

            setTimeout(() => {
                column3.style.opacity = '1';
            }, 10);
        }
    }

    // 4. seviye menüler için hover efekti kurulumu
    function setupFourthLevelMenus(container, isTablet) {
        // 3. sütundaki menü öğelerine hover efekti ekle
        const column3 = container.querySelector('.mega-menu-column-3');
        if (!column3) return;

        // 3. sütun aktif olduğunda hover efektlerini kur
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    const target = mutation.target;
                    if (target.classList.contains('active') && target.classList.contains('mega-menu-column-3')) {
                        setupColumn3HoverEffects(container, isTablet);
                    }
                }
            });
        });

        observer.observe(column3, { attributes: true });
    }

    // 3. sütun hover efektlerini kur
    function setupColumn3HoverEffects(container, isTablet) {
        const column3 = container.querySelector('.mega-menu-column-3');
        if (!column3) return;

        const thirdLevelItems = column3.querySelectorAll('li > a');

        thirdLevelItems.forEach(function(thirdItem, index) {
            let hoverTimer;

            thirdItem.addEventListener('mouseenter', function() {
                // Mobil kontrolü
                if (window.innerWidth <= 768) return;

                // Önceki timer'ı temizle
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                }

                // Kısa gecikme ile hover efektini uygula
                hoverTimer = setTimeout(() => {
                    // 3. sütundaki tüm öğelerden active sınıfını kaldır
                    thirdLevelItems.forEach(item => item.classList.remove('active'));

                    // Bu öğeye active sınıfı ekle
                    this.classList.add('active');

                    // 4. seviye alt kategorileri göster
                    showFourthLevelCategories(container, this, index, isTablet);
                }, 100);
            });

            thirdItem.addEventListener('mouseleave', function() {
                if (hoverTimer) {
                    clearTimeout(hoverTimer);
                }
            });
        });
    }

    // 4. seviye kategorileri göster
    function showFourthLevelCategories(container, menuItem, index, isTablet) {
        const column4 = container.querySelector('.mega-menu-column-4');

        // Bu menü öğesinin alt menüsünü bul
        const parentLi = menuItem.closest('li');
        const subMenuUl = parentLi ? parentLi.querySelector('ul') : null;

        if (!subMenuUl || subMenuUl.children.length === 0) return;

        // 4. sütunu göster ve içeriği doldur
        if (column4) {
            column4.innerHTML = '';

            const ul = document.createElement('ul');
            // Alt menü öğelerini 4. sütuna kopyala
            const subItems = Array.from(subMenuUl.children);
            subItems.forEach(subItem => {
                const clonedItem = subItem.cloneNode(true);

                // Orijinal öğenin has-submenu sınıfını kontrol et ve koru
                const originalLink = subItem.querySelector('a');
                const clonedLink = clonedItem.querySelector('a');
                const originalSubMenu = subItem.querySelector('ul');

                if (originalLink && clonedLink && originalSubMenu && originalSubMenu.children.length > 0) {
                    clonedLink.classList.add('has-submenu');
                }

                ul.appendChild(clonedItem);
            });

            column4.appendChild(ul);

            // 4. sütunu göster
            column4.style.display = 'block';
            column4.classList.add('active');

            setTimeout(() => {
                column4.style.opacity = '1';
            }, 10);
        }
    }

    // Debug fonksiyonu - menü yapısını konsola yazdır
    function debugMenuStructure() {
        const megaMenuItems = document.querySelectorAll('.has-mega-menu');
        console.log('=== MEGA MENU DEBUG ===');

        megaMenuItems.forEach((item, index) => {
            const menuText = item.querySelector('a').textContent.trim();
            console.log(`${index + 1}. Ana Menü: ${menuText}`);

            const subMenu = item.querySelector('.sub-menu');
            if (subMenu) {
                const container = subMenu.querySelector('.sub-menu-container');
                if (container) {
                    const columns = container.querySelectorAll('[class*="mega-menu-column"]');
                    console.log(`   Sütun sayısı: ${columns.length}`);

                    columns.forEach((column, colIndex) => {
                        const columnClass = column.className;
                        const items = column.querySelectorAll('li');
                        console.log(`   ${columnClass}: ${items.length} öğe`);

                        items.forEach((li, itemIndex) => {
                            const link = li.querySelector('a');
                            if (link) {
                                console.log(`     - ${link.textContent.trim()}`);
                            }
                        });
                    });
                }
            }
            console.log('---');
        });
    }

    // Pencere boyutu değiştiğinde mega menüyü yeniden düzenle
    window.addEventListener('resize', function() {
        const activeMegaMenu = document.querySelector('.main-navigation ul li.mega-menu-active');
        if (activeMegaMenu) {
            const subMenuContainer = activeMegaMenu.querySelector('.sub-menu-container');
            // Sadece bloksuz mega menüler için yeniden düzenle
            if (subMenuContainer && !subMenuContainer.querySelector('.mega-menu-page-content')) {
                // Kısa bir gecikme ile yeniden düzenle
                setTimeout(() => {
                    initializeMegaMenuHover(activeMegaMenu);
                }, 100);
            }
        }
    });

    // Debug modunda menü yapısını yazdır
    if (window.location.search.includes('debug_menu=1')) {
        setTimeout(debugMenuStructure, 1000);
    }

});

/**
 * Demir <PERSON> Search Styles
 * 
 * @package demir
 */

/* Search Form Styles */
.demir-ajax-search-form {
    position: relative;
    width: 100%;
}

.demir-ajax-search-form .search-field-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.demir-ajax-search-form .search-field {
    width: 100%;
    padding: 12px 50px 12px 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    background-color: #fff;
    transition: border-color 0.3s ease;
}

.demir-ajax-search-form .search-field:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 1px #007cba;
}

.demir-ajax-search-form .search-submit {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    padding: 8px;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.demir-ajax-search-form .search-submit:hover {
    color: #007cba;
}

.demir-ajax-search-form .search-submit svg {
    display: block;
    width: 16px;
    height: 16px;
}

/* Results Container */
.demir-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    max-height: 400px;
    overflow-y: auto;
}

/* Loading State */
.demir-search-loading {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* No Results */
.demir-search-no-results {
    padding: 20px;
    text-align: center;
    color: #666;
    font-style: italic;
}

/* Results List */
.demir-search-results-list {
    padding: 0;
    margin: 0;
}

/* Individual Result Item */
.demir-search-result-item {
    border-bottom: 1px solid #eee;
}

.demir-search-result-item:last-child {
    border-bottom: none;
}

.demir-search-result-link {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    text-decoration: none;
    color: inherit;
    transition: background-color 0.3s ease;
}

.demir-search-result-link:hover {
    background-color: #f8f9fa;
    text-decoration: none;
}

/* Result Image */
.demir-search-result-image {
    flex-shrink: 0;
    width: 50px;
    height: 50px;
    margin-right: 12px;
    border-radius: 4px;
    overflow: hidden;
    background-color: #f5f5f5;
}

.demir-search-result-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Result Content */
.demir-search-result-content {
    flex: 1;
    min-width: 0;
}

.demir-search-result-title {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 600;
    line-height: 1.3;
    color: #333;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.demir-search-result-price {
    margin: 0 0 4px 0;
    font-size: 13px;
    font-weight: 600;
    color: #007cba;
}

.demir-search-result-description {
    margin: 0;
    font-size: 12px;
    line-height: 1.4;
    color: #666;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* View All Link */
.demir-search-view-all {
    border-top: 1px solid #eee;
    padding: 12px 16px;
    background-color: #f8f9fa;
}

.demir-search-view-all-link {
    display: block;
    text-align: center;
    font-size: 13px;
    font-weight: 600;
    color: #007cba;
    text-decoration: none;
    transition: color 0.3s ease;
}

.demir-search-view-all-link:hover {
    color: #005a87;
    text-decoration: none;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .demir-search-results {
        max-height: 300px;
    }
    
    .demir-search-result-link {
        padding: 10px 12px;
    }
    
    .demir-search-result-image {
        width: 40px;
        height: 40px;
        margin-right: 10px;
    }
    
    .demir-search-result-title {
        font-size: 13px;
    }
    
    .demir-search-result-price {
        font-size: 12px;
    }
    
    .demir-search-result-description {
        font-size: 11px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .demir-ajax-search-form .search-field {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .demir-ajax-search-form .search-field:focus {
        border-color: #63b3ed;
        box-shadow: 0 0 0 1px #63b3ed;
    }
    
    .demir-search-results {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .demir-search-result-link {
        color: #e2e8f0;
    }
    
    .demir-search-result-link:hover {
        background-color: #4a5568;
    }
    
    .demir-search-result-title {
        color: #e2e8f0;
    }
    
    .demir-search-view-all {
        background-color: #4a5568;
        border-color: #718096;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .demir-ajax-search-form .search-field {
        border-width: 2px;
    }
    
    .demir-search-results {
        border-width: 2px;
    }
    
    .demir-search-result-item {
        border-bottom-width: 2px;
    }
}

/* demir <PERSON><PERSON>yling */

.select2-dropdown { 
	z-index: 1052 !important; 
} 

.dokan-form-horizontal label,
.dokan-input-group-addon,
.dokan-dashboard .dokan-dashboard-content .dokan-page-help,
.dokan-alert,
.dokan-message,
.dokan-info,
.dokan-error,
.dokan-pagination,
.dokan-store-info,
.dokan-store-open-close,
.dokan-single-store .dokan-store-tabs ul li,
.pagination-wrap ul.pagination,
.dokan-table > thead > tr > th,
.dokan-dashboard-wrap .select2-container,
.dokan-dashboard .select2-results__option,
.dokan-order-filter-serach input[type="text"],
.dokan-product-listing .dokan-product-listing-area table.product-listing-table td,
.store-wrapper .featured-label,
#dokan-store-listing-filter-wrap,
.store-address .country {
	font-size: 14px;
}

/* Single Product Editor */

.dokan-orders-content .dokan-orders-area ul.order-statuses-filter {
	margin: 0;
}

.dokan-dashboard.single-product .dokan-dashboard-wrap {
	max-width: calc(1170px + 5.2325em);
	margin-right: auto;
	margin-bottom: 40px;
	margin-left: auto;
	padding-right: 2.617924em;
	padding-left: 2.617924em;
}

.dokan-dashboard.single-product .below-content .col-full,
.dokan-dashboard.single-product .site-footer .col-full {
	max-width: calc(1170px + 2.617924em);
	margin-right: auto;
	margin-left: auto;
	padding-right: 2.617924em;
	padding-left: 2.617924em;
}

@media (max-width: 992px) {

	.dokan-dashboard.single-product .dokan-dashboard-wrap,
	.dokan-dashboard.single-product .below-content .col-full,
	.dokan-dashboard.single-product .site-footer .col-full {
		padding-right: 0;
		padding-left: 0;
	}

}

@media (max-width: 767px) {

	.dokan-dashboard .dokan-dash-sidebar {
		margin-bottom: 30px;
	}
}

.dokan-dashboard.single-product .site-content .col-full {
	background-color: inherit;
}

.mce-container-body button:hover {
	background-color: #fff;
}

.product-edit-new-container label input[type="checkbox"] {
	position: relative;
	top: 2px;
}

/* Dashboard */

.dokan-dashboard-wrap form,
.dokan-dashboard-wrap fieldset {
	margin: 0;
	padding: 0;
}

.dokan-order-filter-serach input[type="text"],
.dokan-product-search-form input[type="text"] {
	height: 34px;
}

select.dokan-form-control {
	height: 32px;
	background-position: center right 5px;
	font-size: 14px;
	line-height: 31px;
}

.entry-content .dokan-dashboard-wrap article a:not(.elementor-button) {
	border-bottom: initial;
	color: #222;
}

.site-main .entry-content .dokan-dashboard-wrap article a.dokan-add-new-product {
	color: #fff;
}

.entry-content .pagination-wrap ul.pagination > li > a {
	border-bottom: 1px solid #ddd;
}


.entry-content .dokan-dashboard-wrap article a.dokan-btn,
.entry-content .dokan-dashboard-wrap article .dokan-btn {
	border: none;
}

input.dokan-form-control {
	display: block;
	padding: 4px 6px;
	font-size: 14px;
}

/* Vendor Info tab */

.woocommerce-Tabs-panel--seller .star-rating {
	display: inline-block;
}

.woocommerce-Tabs-panel--seller ul {
	margin: 0;
	list-style: none;
}

.woocommerce-Tabs-panel--seller ul li {
	margin-bottom: 6px;
}

.woocommerce-Tabs-panel--seller ul .clearfix .text {
	position: relative;
	top: -4px;
	margin-left: 5px;
	font-size: 13px;
}

/* Store Listing */

#dokan-seller-listing-wrap.list-view .dokan-seller-wrap .dokan-single-seller .store-wrapper .store-content .store-data-container .store-data h2 {
	margin-bottom: 0;
	font-size: 20px;
}

.dokan-seller-rating p.rating {
	margin-bottom: 0;
	font-size: 14px;
}

#dokan-store-listing-filter-wrap {
	padding: 10px 20px;
}

.store-data {
	margin-top: 20px;
}

#dokan-store-listing-filter-wrap .right .item select {
	margin-left: 5px;
	padding-left: 10px;
	background: url(../../../assets/images/downarrow.gif) center right 5px no-repeat;
	background-size: 15px 15px;
}

/* Store Single */

body.dokan-store .demir-archive {
	margin-bottom: 0;
}

.dokan-store .content-area {
	width: 100%;
}

.dokan-store .col-full p#breadcrumbs {
	display: none;
}

.profile-info-head img {
	display: block;
	margin: 0 auto;
}

.dokan-store-tabs {
	margin-bottom: 40px;
}

.dokan-single-store .dokan-store-tabs ul li {
	margin-bottom: 0;
}

#dokan-secondary .widget {
	margin-bottom: 2em;
}

#dokan-secondary .dokan-store-open-close .open-close-day {
	display: flex;
	padding-top: 3px;
	color: #444;
}

.store-cat-stack-dokan {
	font-size: 15px;
}

.store-cat-stack-dokan .children {
	margin-top: 0.7em;
	margin-bottom: 0.7em;
	margin-left: 0;
	padding-left: 15px;
	border-left: 1px solid #eee;
}

.store-cat-stack-dokan a {
	color: #777;
}

.seller-form input[type="submit"].dokan-btn-theme {
	border: none;
}

.dokan-pagination-container .dokan-pagination {
	margin-bottom: 40px;
}

.site-main .dokan-pagination-container .dokan-pagination a {
	padding-top: 6px;
	padding-bottom: 6px;
	color: #777;
}

.site-main .dokan-pagination-container .dokan-pagination a:hover {
	border-color: #ccc;
}

/* My Orders */

table.my_account_orders.table-striped tr th {
	padding-top: 0;
	padding-bottom: 0.7em;
}

table.my_account_orders.table-striped tr td {
	padding-top: 0.7em;
	padding-bottom: 0.7em;
	border-top: 1px solid #eee;
	font-size: 14px;
	vertical-align: middle;
}

table.my_account_orders.table-striped tr:nth-child(odd) td {
	background-color: #f8f8f8;
}

table.my_account_orders.table-striped tr td.order-actions {
	text-align: right;
}

table.my_account_orders.table-striped tr td.order-actions .button {
	margin-right: 0;
	margin-left: 10px;
	padding: 5px 10px;
	border-radius: 3px;
	font-size: 13px;
	transition: 0.2s all;
}

table.my_account_orders.table-striped tr td.order-actions .button:hover {
	background-color: #444;
}


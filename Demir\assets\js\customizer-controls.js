/**
 * Demir Customizer Controls JavaScript
 * 
 * Handles customizer control interactions and enhancements
 */

(function($) {
    'use strict';

    // Initialize when customizer is ready
    wp.customize.bind('ready', function() {
        
        // Handle radio-image controls
        initRadioImageControls();
        
        // Handle typography controls
        initTypographyControls();
        
        // Handle conditional controls
        initConditionalControls();
        
        // Handle color controls
        initColorControls();
        
        // <PERSON>le slider controls
        initSliderControls();
    });

    /**
     * Initialize radio-image controls
     */
    function initRadioImageControls() {
        $('.customize-control-radio input[type="radio"]').each(function() {
            var $this = $(this);
            var $control = $this.closest('.customize-control');
            
            // Check if this is a radio-image control (has image choices)
            if ($control.find('img').length > 0) {
                $control.addClass('customize-control-radio-image');
                
                // Style the radio buttons
                $this.on('change', function() {
                    $control.find('img').removeClass('selected');
                    $control.find('input:checked').siblings('img').addClass('selected');
                });
                
                // Trigger initial state
                $this.trigger('change');
            }
        });
    }

    /**
     * Initialize typography controls
     */
    function initTypographyControls() {
        // Group typography controls together
        $('.customize-control').each(function() {
            var $control = $(this);
            var controlId = $control.attr('id');
            
            // Check if this is a typography sub-control
            if (controlId && controlId.match(/_font_family|_variant|_font_size|_line_height|_letter_spacing|_color|_text_transform$/)) {
                var baseId = controlId.replace(/_font_family|_variant|_font_size|_line_height|_letter_spacing|_color|_text_transform$/, '');
                $control.addClass('typography-control').attr('data-typography-group', baseId);
            }
        });
    }

    /**
     * Initialize conditional controls
     */
    function initConditionalControls() {
        // Handle active_callback functionality
        wp.customize.bind('change', function(setting) {
            checkConditionalControls();
        });
        
        // Initial check
        checkConditionalControls();
    }

    /**
     * Check and show/hide conditional controls
     */
    function checkConditionalControls() {
        $('.customize-control').each(function() {
            var $control = $(this);
            var controlId = $control.attr('id');
            
            // This is a simplified version - in a full implementation,
            // you would parse the active_callback conditions from PHP
            // and evaluate them here
        });
    }

    /**
     * Initialize color controls
     */
    function initColorControls() {
        // Enhance color picker controls
        $('.customize-control-color .wp-color-picker').each(function() {
            var $this = $(this);
            
            // Add custom styling or functionality if needed
            $this.wpColorPicker({
                change: function(event, ui) {
                    // Trigger change event for live preview
                    $this.trigger('change');
                }
            });
        });
    }

    /**
     * Initialize slider controls
     */
    function initSliderControls() {
        $('.customize-control input[type="number"]').each(function() {
            var $input = $(this);
            var $control = $input.closest('.customize-control');
            
            // Check if this should be a slider
            if ($input.attr('min') !== undefined && $input.attr('max') !== undefined) {
                var min = parseInt($input.attr('min')) || 0;
                var max = parseInt($input.attr('max')) || 100;
                var step = parseInt($input.attr('step')) || 1;
                var value = parseInt($input.val()) || min;
                
                // Create slider element
                var $slider = $('<div class="customize-control-slider"></div>');
                $input.after($slider);
                
                // Initialize slider
                $slider.slider({
                    min: min,
                    max: max,
                    step: step,
                    value: value,
                    slide: function(event, ui) {
                        $input.val(ui.value).trigger('change');
                    }
                });
                
                // Update slider when input changes
                $input.on('input change', function() {
                    $slider.slider('value', parseInt($(this).val()) || min);
                });
            }
        });
    }

    /**
     * Add custom CSS for enhanced controls
     */
    function addCustomStyles() {
        var css = `
            .customize-control-radio-image img {
                max-width: 100%;
                height: auto;
                border: 2px solid transparent;
                cursor: pointer;
                transition: border-color 0.3s ease;
            }
            
            .customize-control-radio-image img.selected,
            .customize-control-radio-image img:hover {
                border-color: #0073aa;
            }
            
            .customize-control-radio-image input[type="radio"] {
                display: none;
            }
            
            .typography-control {
                border-left: 3px solid #0073aa;
                padding-left: 10px;
                margin-left: 5px;
            }
            
            .customize-control-slider {
                margin: 10px 0;
            }
            
            .kirki-separator {
                margin: 10px -12px;
                padding: 12px 12px;
                color: #111;
                text-transform: uppercase;
                letter-spacing: 1px;
                border-top: 1px solid #ddd;
                border-bottom: 1px solid #ddd;
                background-color: #fff;
                cursor: default;
                font-weight: bold;
            }
        `;
        
        $('<style>').prop('type', 'text/css').html(css).appendTo('head');
    }

    // Add styles when document is ready
    $(document).ready(function() {
        addCustomStyles();
    });

})(jQuery);

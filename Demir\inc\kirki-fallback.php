<?php
/**
 * Demir Native Customizer Framework
 *
 * A complete replacement for Kirki plugin functionality
 * Provides customizer controls, CSS output, and font management
 *
 * @package CommerceGurus
 * @subpackage demir
 */

// Exit if accessed directly.
if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/**
 * Demir Native Customizer Framework
 *
 * This class provides a complete customizer framework that replaces Kirki functionality.
 * It handles customizer controls, CSS output generation, and Google Fonts loading.
 */
class demir_Kirki {

	/**
	 * Configuration storage
	 * @var array
	 */
	protected static $config = array();

	/**
	 * Fields storage
	 * @var array
	 */
	protected static $fields = array();

	/**
	 * Panels storage
	 * @var array
	 */
	protected static $panels = array();

	/**
	 * Sections storage
	 * @var array
	 */
	protected static $sections = array();

	/**
	 * Constructor - Initialize the customizer framework
	 */
	public function __construct() {
		// If Kirki exists, let it handle everything
		if ( class_exists( 'Kirki' ) ) {
			return;
		}

		// Initialize our native customizer framework
		add_action( 'customize_register', array( $this, 'register_customizer_elements' ) );
		add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_styles' ), 20 );
		add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_fonts' ) );
		add_action( 'customize_controls_enqueue_scripts', array( $this, 'customize_controls_scripts' ) );
		add_action( 'customize_preview_init', array( $this, 'customize_preview_scripts' ) );
	}

	/**
	 * Register customizer panels, sections, and controls
	 *
	 * @param WP_Customize_Manager $wp_customize
	 */
	public function register_customizer_elements( $wp_customize ) {
		// Register panels
		foreach ( self::$panels as $panel_id => $panel_args ) {
			$wp_customize->add_panel( $panel_id, $panel_args );
		}

		// Register sections
		foreach ( self::$sections as $section_id => $section_args ) {
			$wp_customize->add_section( $section_id, $section_args );
		}

		// Register controls
		foreach ( self::$fields as $field_id => $field ) {
			$this->add_customizer_control( $wp_customize, $field_id, $field );
		}
	}

	/**
	 * Add a customizer control based on field type
	 *
	 * @param WP_Customize_Manager $wp_customize
	 * @param string $field_id
	 * @param array $field
	 */
	private function add_customizer_control( $wp_customize, $field_id, $field ) {
		// Add setting first
		$setting_args = array(
			'default'           => isset( $field['default'] ) ? $field['default'] : '',
			'type'              => 'theme_mod',
			'capability'        => 'edit_theme_options',
			'sanitize_callback' => array( $this, 'sanitize_field' ),
			'transport'         => isset( $field['transport'] ) ? $field['transport'] : 'refresh',
		);

		$wp_customize->add_setting( $field['settings'], $setting_args );

		// Determine control type and add control
		$control_args = array(
			'label'       => isset( $field['label'] ) ? $field['label'] : '',
			'description' => isset( $field['description'] ) ? $field['description'] : '',
			'section'     => $field['section'],
			'settings'    => $field['settings'],
			'priority'    => isset( $field['priority'] ) ? $field['priority'] : 10,
		);

		// Add field-specific arguments
		if ( isset( $field['choices'] ) ) {
			$control_args['choices'] = $field['choices'];
		}

		// Add control based on type
		switch ( $field['type'] ) {
			case 'color':
				$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, $field_id, $control_args ) );
				break;
			case 'image':
				$wp_customize->add_control( new WP_Customize_Image_Control( $wp_customize, $field_id, $control_args ) );
				break;
			case 'upload':
				$wp_customize->add_control( new WP_Customize_Upload_Control( $wp_customize, $field_id, $control_args ) );
				break;
			case 'select':
			case 'radio':
				$wp_customize->add_control( $field_id, $control_args );
				break;
			case 'checkbox':
			case 'toggle':
				$control_args['type'] = 'checkbox';
				$wp_customize->add_control( $field_id, $control_args );
				break;
			case 'textarea':
				$control_args['type'] = 'textarea';
				$wp_customize->add_control( $field_id, $control_args );
				break;
			case 'number':
			case 'slider':
				$control_args['type'] = 'number';
				if ( isset( $field['choices'] ) ) {
					$control_args['input_attrs'] = array(
						'min'  => isset( $field['choices']['min'] ) ? $field['choices']['min'] : 0,
						'max'  => isset( $field['choices']['max'] ) ? $field['choices']['max'] : 100,
						'step' => isset( $field['choices']['step'] ) ? $field['choices']['step'] : 1,
					);
				}
				$wp_customize->add_control( $field_id, $control_args );
				break;
			case 'typography':
				// For typography, we'll create multiple controls
				$this->add_typography_controls( $wp_customize, $field_id, $field, $control_args );
				break;
			case 'custom':
				// Custom HTML content
				$control_args['type'] = 'hidden';
				$control_args['description'] = $field['default'];
				$wp_customize->add_control( $field_id, $control_args );
				break;
			case 'radio-image':
				// For radio-image, we'll use radio with custom styling
				$control_args['type'] = 'radio';
				$wp_customize->add_control( $field_id, $control_args );
				break;
			default:
				// Default to text input
				$wp_customize->add_control( $field_id, $control_args );
				break;
		}
	}

	/**
	 * Add typography controls (multiple controls for one typography field)
	 *
	 * @param WP_Customize_Manager $wp_customize
	 * @param string $field_id
	 * @param array $field
	 * @param array $base_control_args
	 */
	private function add_typography_controls( $wp_customize, $field_id, $field, $base_control_args ) {
		$typography_properties = array(
			'font-family'    => 'Font Family',
			'variant'        => 'Font Weight',
			'font-size'      => 'Font Size',
			'line-height'    => 'Line Height',
			'letter-spacing' => 'Letter Spacing',
			'color'          => 'Color',
			'text-transform' => 'Text Transform',
		);

		foreach ( $typography_properties as $property => $label ) {
			$setting_id = $field['settings'] . '_' . str_replace( '-', '_', $property );
			$control_id = $field_id . '_' . str_replace( '-', '_', $property );

			// Add setting
			$default_value = '';
			if ( isset( $field['default'][ $property ] ) ) {
				$default_value = $field['default'][ $property ];
			}

			$wp_customize->add_setting( $setting_id, array(
				'default'           => $default_value,
				'type'              => 'theme_mod',
				'capability'        => 'edit_theme_options',
				'sanitize_callback' => array( $this, 'sanitize_field' ),
				'transport'         => isset( $field['transport'] ) ? $field['transport'] : 'refresh',
			) );

			// Add control
			$control_args = $base_control_args;
			$control_args['label'] = $label;
			$control_args['settings'] = $setting_id;

			if ( $property === 'color' ) {
				$wp_customize->add_control( new WP_Customize_Color_Control( $wp_customize, $control_id, $control_args ) );
			} elseif ( $property === 'font-family' ) {
				$control_args['type'] = 'select';
				$control_args['choices'] = $this->get_font_choices();
				$wp_customize->add_control( $control_id, $control_args );
			} elseif ( $property === 'text-transform' ) {
				$control_args['type'] = 'select';
				$control_args['choices'] = array(
					'none'       => 'None',
					'uppercase'  => 'Uppercase',
					'lowercase'  => 'Lowercase',
					'capitalize' => 'Capitalize',
				);
				$wp_customize->add_control( $control_id, $control_args );
			} else {
				$control_args['type'] = 'text';
				$wp_customize->add_control( $control_id, $control_args );
			}
		}
	}

	/**
	 * Get font choices for typography controls
	 *
	 * @return array
	 */
	private function get_font_choices() {
		return array(
			'Arial, sans-serif'                           => 'Arial',
			'Helvetica, Arial, sans-serif'                => 'Helvetica',
			'"Times New Roman", Times, serif'             => 'Times New Roman',
			'Georgia, serif'                              => 'Georgia',
			'"Courier New", Courier, monospace'           => 'Courier New',
			'Verdana, Geneva, sans-serif'                 => 'Verdana',
			'Inter'                                       => 'Inter',
			'"Open Sans", sans-serif'                     => 'Open Sans',
			'Roboto, sans-serif'                          => 'Roboto',
			'"Source Sans Pro", sans-serif'               => 'Source Sans Pro',
		);
	}

	/**
	 * Sanitize field values
	 *
	 * @param mixed $value
	 * @return mixed
	 */
	public function sanitize_field( $value ) {
		// Basic sanitization - can be extended based on field type
		if ( is_string( $value ) ) {
			return sanitize_text_field( $value );
		}
		return $value;
	}

	/**
	 * Get the value of an option from the db.
	 *
	 * @param    string $config_id    The ID of the configuration corresponding to this field
	 * @param    string $field_id     The field_id (defined as 'settings' in the field arguments)
	 *
	 * @return 	mixed 	the saved value of the field.
	 */
	public static function get_option( $config_id = '', $field_id = '' ) {
		// if Kirki exists, use it.
		if ( class_exists( 'Kirki' ) ) {
			return Kirki::get_option( $config_id, $field_id );
		}
		// Kirki does not exist, continue with our custom implementation.
		// Get the default value of the field
		$default = '';
		if ( isset( self::$fields[ $field_id ] ) && isset( self::$fields[ $field_id ]['default'] ) ) {
			$default = self::$fields[ $field_id ]['default'];
		}
		// Make sure the config is defined
		if ( isset( self::$config[ $config_id ] ) ) {
			if ( 'option' == self::$config[ $config_id ]['option_type'] ) {
				// check if we're using serialized options
				if ( isset( self::$config[ $config_id ]['option_name'] ) && ! empty( self::$config[ $config_id ]['option_name'] ) ) {
					// Get all our options
					$all_options = get_option( self::$config[ $config_id ]['option_name'], array() );
					// If our option is not saved, return the default value.
					if ( ! isset( $all_options[ $field_id ] ) ) {
						return $default;
					}
					// Option was set, return its value unserialized.
					return maybe_unserialize( $all_options[ $field_id ] );
				}
				// If we're not using serialized options, get the value and return it.
				// We'll be using a dummy default here to check if the option has been set or not.
				// We'll be using md5 to make sure it's randomish and impossible to be actually set by a user.
				$dummy	 = md5( $config_id . '_UNDEFINED_VALUE' );
				$value	 = get_option( $field_id, $dummy );
				// setting has not been set, return default.
				if ( $dummy == $value ) {
					return $default;
				}
				return $value;
			}
			// We're not using options so fallback to theme_mod
			return get_theme_mod( $field_id, $default );
		}
	}

	/**
	 * Create a new panel
	 *
	 * @param   string      the ID for this panel
	 * @param   array       the panel arguments
	 */
	public static function add_panel( $id = '', $args = array() ) {
		if ( class_exists( 'Kirki' ) ) {
			Kirki::add_panel( $id, $args );
			return;
		}
		// Store panel for later registration
		self::$panels[ $id ] = $args;
	}

	/**
	 * Create a new section
	 *
	 * @param   string      the ID for this section
	 * @param   array       the section arguments
	 */
	public static function add_section( $id, $args ) {
		if ( class_exists( 'Kirki' ) ) {
			Kirki::add_section( $id, $args );
			return;
		}
		// Store section for later registration
		self::$sections[ $id ] = $args;
	}

	/**
	 * Sets the configuration options.
	 *
	 * @param    string $config_id    The configuration ID
	 * @param    array  $args         The configuration arguments
	 */
	public static function add_config( $config_id, $args = array() ) {
		// if Kirki exists, use it.
		if ( class_exists( 'Kirki' ) ) {
			Kirki::add_config( $config_id, $args );
			return;
		}
		// Kirki does not exist, set the config arguments
		$config[ $config_id ] = $args;
		// Make sure an option_type is defined
		if ( ! isset( self::$config[ $config_id ]['option_type'] ) ) {
			self::$config[ $config_id ]['option_type'] = 'theme_mod';
		}
	}

	/**
	 * Create a new field
	 *
	 * @param    string $config_id    The configuration ID
	 * @param    array  $args         The field's arguments
	 */
	public static function add_field( $config_id, $args ) {
		// if Kirki exists, use it.
		if ( class_exists( 'Kirki' ) ) {
			Kirki::add_field( $config_id, $args );
			return;
		}
		// Kirki was not located, so we'll need to add our fields here.
		// check that the "settings" & "type" arguments have been defined
		if ( isset( $args['settings'] ) && isset( $args['type'] ) ) {
			// Make sure we add the config_id to the field itself.
			// This will make it easier to get the value when generating the CSS later.
			if ( ! isset( $args['kirki_config'] ) ) {
				$args['kirki_config'] = $config_id;
			}
			// Background fields need to be built separately
			if ( 'background' == $args['type'] && isset( $args['output'] ) ) {
				if ( isset( $args['default'] ) && is_array( $args['default'] ) ) {
					foreach ( $args['default'] as $default_property => $default_value ) {
						$subfield = $args;
						// No need to process the opacity, it is factored in the color control.
						if ( 'opacity' == $key ) {
							continue;
						}
						$key			 = esc_attr( $key );
						$setting		 = $key;
						$output_property = 'background-' . $key;
						if ( 'attach' == $key ) {
							$output_property = 'background-attachment';
						}
						if ( is_string( $subfield['output'] ) ) {
							$subfield['output'] = array( array(
									'element'	 => $args['output'],
									'property'	 => $output_property,
								),
							);
						} else {
							foreach ( $subfield['output'] as $key => $output ) {
								$subfield['output'][ $key ]['property'] = $output_property;
							}
						}
						$type = 'select';
						if ( in_array( $key, array( 'color', 'image' ) ) ) {
							$type = $key;
						}
						$property_setting					 = esc_attr( $args['settings'] ) . '_' . $setting;
						self::$fields[ $property_setting ]	 = $subfield;
					}
				}
			}
			self::$fields[ $args['settings'] ] = $args;
		}// End if().
	}

	/**
	 * Enqueues the stylesheet
	 */
	public function enqueue_styles() {
		// If Kirki exists there's no need to proceed any further
		if ( class_exists( 'Kirki' ) ) {
			return;
		}
		// Get our inline styles
		$styles = $this->get_styles();
		// If we have some styles to add, add them now.
		if ( ! empty( $styles ) ) {
			// enqueue the theme's style.css file
			$current_theme = ( wp_get_theme() );
			wp_enqueue_style( $current_theme->stylesheet . '_no-kirki', get_stylesheet_uri(), null, null );
			wp_add_inline_style( $current_theme->stylesheet . '_no-kirki', $styles );
		}
	}

	/**
	 * Gets all our styles and returns them as a string.
	 */
	public function get_styles() {
		// Get an array of all our fields
		$fields = self::$fields;
		// Check if we need to exit early
		if ( empty( self::$fields ) || ! is_array( $fields ) ) {
			return '';
		}
		// initially we're going to format our styles as an array.
		// This is going to make processing them a lot easier
		// and make sure there are no duplicate styles etc.
		$css = array();
		// start parsing our fields
		foreach ( $fields as $field ) {
			// No need to process fields without an output, or an improperly-formatted output
			if ( ! isset( $field['output'] ) || empty( $field['output'] ) || ! is_array( $field['output'] ) ) {
				continue;
			}

			// Handle typography fields differently
			if ( 'typography' == $field['type'] ) {
				$css = array_merge_recursive( $css, $this->process_typography_field( $field ) );
			} else {
				// Get the value of this field
				$value = self::get_option( $field['kirki_config'], $field['settings'] );
				// start parsing the output arguments of the field
				foreach ( $field['output'] as $output ) {
					$defaults = array(
						'element'		 => '',
						'property'		 => '',
						'media_query'	 => 'global',
						'prefix'		 => '',
						'units'			 => '',
						'suffix'		 => '',
						'value_pattern'	 => '$',
						'choice'		 => '',
					);
					$output = wp_parse_args( $output, $defaults );

					// If element is an array, convert it to a string
					if ( is_array( $output['element'] ) ) {
						$output['element'] = array_unique( $output['element'] );
						sort( $output['element'] );
						$output['element'] = implode( ',', $output['element'] );
					}

					// Process the value
					$processed_value = $this->process_field_value( $value, $output, $field['type'] );

					if ( ! empty( $output['element'] ) && ! empty( $output['property'] ) && $processed_value !== '' ) {
						$css[ $output['media_query'] ][ $output['element'] ][ $output['property'] ] = $processed_value;
					}
				}
			}
		}

		// Generate final CSS string
		return $this->generate_css_string( $css );
	}

	/**
	 * Process typography field and return CSS array
	 *
	 * @param array $field
	 * @return array
	 */
	private function process_typography_field( $field ) {
		$css = array();
		$typography_properties = array(
			'font-family', 'variant', 'font-size', 'line-height',
			'letter-spacing', 'color', 'text-transform'
		);

		foreach ( $typography_properties as $property ) {
			$setting_id = $field['settings'] . '_' . str_replace( '-', '_', $property );
			$value = get_theme_mod( $setting_id );

			if ( empty( $value ) ) {
				continue;
			}

			foreach ( $field['output'] as $output ) {
				$defaults = array(
					'element'		 => '',
					'property'		 => $property,
					'media_query'	 => 'global',
					'prefix'		 => '',
					'units'			 => '',
					'suffix'		 => '',
				);
				$output = wp_parse_args( $output, $defaults );

				if ( is_array( $output['element'] ) ) {
					$output['element'] = implode( ',', $output['element'] );
				}

				// Process specific typography properties
				if ( $property === 'variant' ) {
					// Handle font-weight and font-style
					$font_weight = str_replace( 'italic', '', $value );
					$font_weight = ( in_array( $font_weight, array( '', 'regular' ) ) ) ? '400' : $font_weight;
					$css[ $output['media_query'] ][ $output['element'] ]['font-weight'] = $font_weight;

					if ( strpos( $value, 'italic' ) !== false ) {
						$css[ $output['media_query'] ][ $output['element'] ]['font-style'] = 'italic';
					}
				} elseif ( $property === 'font-family' ) {
					// Add quotes if needed
					if ( strpos( $value, ' ' ) !== false && strpos( $value, '"' ) === false ) {
						$value = '"' . $value . '"';
					}
					$css[ $output['media_query'] ][ $output['element'] ][ $property ] = $value;
				} else {
					$css[ $output['media_query'] ][ $output['element'] ][ $property ] = $output['prefix'] . $value . $output['units'] . $output['suffix'];
				}
			}
		}

		return $css;
	}

	/**
	 * Process field value based on type
	 *
	 * @param mixed $value
	 * @param array $output
	 * @param string $field_type
	 * @return string
	 */
	private function process_field_value( $value, $output, $field_type ) {
		if ( is_array( $value ) ) {
			if ( $field_type === 'spacing' ) {
				// Handle spacing fields
				$processed_values = array();
				foreach ( $value as $key => $subvalue ) {
					$property = empty( $output['property'] ) ? $key : $output['property'] . '-' . $key;
					$processed_values[ $property ] = $subvalue;
				}
				return $processed_values;
			} elseif ( $field_type === 'multicolor' && ! empty( $output['choice'] ) ) {
				$value = isset( $value[ $output['choice'] ] ) ? $value[ $output['choice'] ] : '';
			} else {
				// For other array values, convert to string or skip
				$value = is_string( $value ) ? $value : '';
			}
		}

		// Apply value pattern
		if ( ! empty( $output['value_pattern'] ) ) {
			$value = str_replace( '$', $value, $output['value_pattern'] );
		}

		// Add prefix, suffix, and units
		return $output['prefix'] . $value . $output['units'] . $output['suffix'];
	}

	/**
	 * Generate CSS string from CSS array
	 *
	 * @param array $css
	 * @return string
	 */
	private function generate_css_string( $css ) {
		$final_css = '';

		if ( ! is_array( $css ) || empty( $css ) ) {
			return '';
		}

		// Parse the generated CSS array and create the CSS string for the output.
		foreach ( $css as $media_query => $styles ) {
			// Handle the media queries
			$final_css .= ( 'global' != $media_query ) ? $media_query . '{' : '';

			foreach ( $styles as $selector => $properties ) {
				$final_css .= $selector . '{';

				foreach ( $properties as $property => $value ) {
					$value = ( is_string( $value ) ) ? $value : '';

					// Make sure background-images are properly formatted
					if ( 'background-image' == $property ) {
						if ( false === strrpos( $value, 'url(' ) && ! empty( $value ) ) {
							$value = 'url("' . esc_url_raw( $value ) . '")';
						}
					} else {
						$value = esc_attr( $value );
					}

					if ( ! empty( $value ) ) {
						$final_css .= $property . ':' . $value . ';';
					}
				}

				$final_css .= '}';
			}

			$final_css .= ( 'global' != $media_query ) ? '}' : '';
		}

		return $final_css;
	}

public function enqueue_fonts() {
	// Check if we need to exit early
	if ( empty( self::$fields ) || ! is_array( self::$fields ) ) {
		return;
	}

	$google_fonts = array();

	foreach ( self::$fields as $field ) {
		// Process typography fields
		if ( isset( $field['type'] ) && 'typography' == $field['type'] ) {
			// Check if we've got everything we need
			if ( ! isset( $field['kirki_config'] ) || ! isset( $field['settings'] ) ) {
				continue;
			}
			$value = self::get_option( $field['kirki_config'], $field['settings'] );
			if ( isset( $value['font-family'] ) && ! empty( $value['font-family'] ) ) {
				// Check if it's a Google Font (not a system font)
				if ( ! $this->is_system_font( $value['font-family'] ) ) {
					$font_family = $value['font-family'];
					$variant = isset( $value['variant'] ) ? $value['variant'] : '400';
					$subset = isset( $value['subset'] ) ? $value['subset'] : 'latin';

					if ( ! isset( $google_fonts[ $font_family ] ) ) {
						$google_fonts[ $font_family ] = array(
							'variants' => array(),
							'subsets' => array(),
						);
					}

					if ( ! in_array( $variant, $google_fonts[ $font_family ]['variants'] ) ) {
						$google_fonts[ $font_family ]['variants'][] = $variant;
					}

					if ( is_array( $subset ) ) {
						$google_fonts[ $font_family ]['subsets'] = array_merge( $google_fonts[ $font_family ]['subsets'], $subset );
					} else {
						if ( ! in_array( $subset, $google_fonts[ $font_family ]['subsets'] ) ) {
							$google_fonts[ $font_family ]['subsets'][] = $subset;
						}
					}
				}
			}
		}
	}

	// Enqueue Google Fonts
	if ( ! empty( $google_fonts ) ) {
		$this->enqueue_google_fonts( $google_fonts );
	}
}

/**
 * Check if a font is a system font
 *
 * @param string $font_family
 * @return bool
 */
private function is_system_font( $font_family ) {
	$system_fonts = array(
		'Arial',
		'Helvetica',
		'Times New Roman',
		'Georgia',
		'Courier New',
		'Verdana',
		'Tahoma',
		'Trebuchet MS',
		'Impact',
		'Comic Sans MS',
	);

	foreach ( $system_fonts as $system_font ) {
		if ( strpos( $font_family, $system_font ) !== false ) {
			return true;
		}
	}

	return false;
}

/**
 * Enqueue Google Fonts
 *
 * @param array $google_fonts
 */
private function enqueue_google_fonts( $google_fonts ) {
	$families = array();

	foreach ( $google_fonts as $font_family => $font_data ) {
		$family_string = str_replace( ' ', '+', $font_family );

		if ( ! empty( $font_data['variants'] ) ) {
			$family_string .= ':' . implode( ',', $font_data['variants'] );
		}

		$families[] = $family_string;
	}

	if ( ! empty( $families ) ) {
		$url = 'https://fonts.googleapis.com/css?family=' . implode( '|', $families ) . '&display=swap';
		$key = 'demir_google_fonts_' . md5( $url );
		wp_enqueue_style( $key, $url, array(), null );
	}
}

/**
 * Enqueue customizer control scripts
 */
public function customize_controls_scripts() {
	wp_enqueue_script(
		'demir-customizer-controls',
		get_template_directory_uri() . '/assets/js/customizer-controls.js',
		array( 'jquery', 'customize-controls' ),
		DEMIR_VERSION,
		true
	);
}

/**
 * Enqueue customizer preview scripts
 */
public function customize_preview_scripts() {
	wp_enqueue_script(
		'demir-customizer-preview',
		get_template_directory_uri() . '/assets/js/customizer-preview.js',
		array( 'jquery', 'customize-preview' ),
		DEMIR_VERSION,
		true
	);
}

	/**
	 * Get all registered fields
	 *
	 * @return array All fields
	 */
	public static function get_all_fields() {
		return self::$fields;
	}

	/**
	 * Get all registered panels
	 *
	 * @return array All panels
	 */
	public static function get_all_panels() {
		return self::$panels;
	}

	/**
	 * Get all registered sections
	 *
	 * @return array All sections
	 */
	public static function get_all_sections() {
		return self::$sections;
	}

	/**
	 * Get all registered configurations
	 *
	 * @return array All configurations
	 */
	public static function get_all_configs() {
		return self::$config;
	}

}

new demir_Kirki();



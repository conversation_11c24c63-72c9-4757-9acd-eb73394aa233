/**
 * User Avatar Dropdown JavaScript
 * Gelismis kullanici avatar dropdown islevsellik
 */

// <PERSON>fa yuklendiginde calistir
document.addEventListener('DOMContentLoaded', function() {
    const avatarButton = document.getElementById('user-avatar-toggle');
    const dropdownMenu = document.getElementById('user-dropdown-menu');

    if (!avatarButton || !dropdownMenu) {
        return; // Elementler bulunamazsa cik
    }

    // Avatar butonuna tiklandiginda dropdown'u ac/kapat
    avatarButton.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();

        const isActive = dropdownMenu.classList.contains('active');

        if (isActive) {
            closeDropdown();
        } else {
            openDropdown();
        }
    });

    // Dropdown disina tiklandiginda kapat
    document.addEventListener('click', function(e) {
        if (!avatarButton.contains(e.target) && !dropdownMenu.contains(e.target)) {
            closeDropdown();
        }
    });

    // ESC tusuna basildiginda kapat
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeDropdown();
        }
    });

    // Dropdown menu itemlarina klavye navigasyonu
    dropdownMenu.addEventListener('keydown', function(e) {
        const menuItems = dropdownMenu.querySelectorAll('.user-menu-item');
        const currentIndex = Array.from(menuItems).findIndex(item => item === document.activeElement);

        switch(e.key) {
            case 'ArrowDown':
                e.preventDefault();
                const nextIndex = currentIndex < menuItems.length - 1 ? currentIndex + 1 : 0;
                menuItems[nextIndex].focus();
                break;
            case 'ArrowUp':
                e.preventDefault();
                const prevIndex = currentIndex > 0 ? currentIndex - 1 : menuItems.length - 1;
                menuItems[prevIndex].focus();
                break;
            case 'Enter':
            case ' ':
                e.preventDefault();
                if (document.activeElement.classList.contains('user-menu-item')) {
                    document.activeElement.click();
                }
                break;
        }
    });

    // Dropdown'u ac
    function openDropdown() {
        dropdownMenu.classList.add('active');
        avatarButton.setAttribute('aria-expanded', 'true');
    }

    // Dropdown'u kapat
    function closeDropdown() {
        dropdownMenu.classList.remove('active');
        avatarButton.setAttribute('aria-expanded', 'false');
    }


});

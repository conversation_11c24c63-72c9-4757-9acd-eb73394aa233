/**
 * Shop Filters Modal JavaScript
 * Magaza sayfasi filtreler modal fonksiyonlari
 */

(function($) {
    'use strict';

    // DOM hazir oldugunda calistir
    $(document).ready(function() {
        initShopFilters();
    });

    function initShopFilters() {
        const modal = $('#shop-filters-modal');
        const toggleBtn = $('.shop-filters-toggle'); // Class seçici kullan
        const closeBtn = $('#shop-filters-close');
        const clearBtn = $('#clear-filters');
        const form = $('#shop-filters-form');

        // Modal acma - tum filtre butonlari icin
        toggleBtn.on('click', function(e) {
            e.preventDefault();
            openModal();
        });

        // Modal kapama - X butonu
        closeBtn.on('click', function(e) {
            e.preventDefault();
            closeModal();
        });

        // Modal kapama - arka plan tiklama
        modal.on('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // ESC tusuna basinca modal kapat
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && modal.is(':visible')) {
                closeModal();
            }
        });

        // Filtreleri temizle
        clearBtn.on('click', function(e) {
            e.preventDefault();
            clearFilters();
        });

        // Form gonderimi
        form.on('submit', function(e) {
            e.preventDefault();
            applyFilters();
        });

        // Checkbox degisikliklerini dinle
        form.find('input[type="checkbox"]').on('change', function() {
            updateFilterCount();
        });

        // Fiyat input degisikliklerini dinle
        form.find('input[type="number"]').on('input', function() {
            updateFilterCount();
        });

        // Sayfa yuklendiginde aktif filtre sayisini guncelle
        updateFilterCount();
    }

    function openModal() {
        const modal = $('#shop-filters-modal');

        // Scroll pozisyonunu kaydet
        const scrollTop = $(window).scrollTop();
        $('body').data('scroll-position', scrollTop);

        // Modal'i ac ve scroll'u engelle - daha iyi yontem
        modal.show();
        $('body').addClass('modal-open');
        $('html').addClass('modal-open');
        // top CSS'ini kaldiriyoruz - header/footer bozulmasini onlemek icin

        // Modal acildiginda checkbox durumlarini URL'den guncelle
        updateCheckboxesFromURL();

        // Modal acildiginda ilk input'a focus ver
        modal.find('input:first').focus();

        // Filtre sayisini guncelle
        updateFilterCount();
    }

    function updateCheckboxesFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        const form = $('#shop-filters-form');

        // Tum checkbox'lari temizle
        form.find('input[type="checkbox"]').prop('checked', false);

        // Kategori checkbox'larini isle
        const productCat = urlParams.get('product_cat');
        if (productCat) {
            const categories = productCat.split(',');
            categories.forEach(function(cat) {
                form.find('input[name="product_cat[]"][value="' + cat + '"]').prop('checked', true);
            });
        }

        // Etiket checkbox'larini isle
        const productTag = urlParams.get('product_tag');
        if (productTag) {
            const tags = productTag.split(',');
            tags.forEach(function(tag) {
                form.find('input[name="product_tag[]"][value="' + tag + '"]').prop('checked', true);
            });
        }

        // Stok durumu checkbox'larini isle
        const stockStatus = urlParams.get('stock_status');
        if (stockStatus) {
            const statuses = stockStatus.split(',');
            statuses.forEach(function(status) {
                form.find('input[name="stock_status[]"][value="' + status + '"]').prop('checked', true);
            });
        }

        // Fiyat inputlarini isle
        const minPrice = urlParams.get('min_price');
        const maxPrice = urlParams.get('max_price');
        if (minPrice) {
            form.find('input[name="min_price"]').val(minPrice);
        }
        if (maxPrice) {
            form.find('input[name="max_price"]').val(maxPrice);
        }
    }

    function closeModal() {
        const modal = $('#shop-filters-modal');

        // Scroll pozisyonunu geri yukle
        const scrollTop = $('body').data('scroll-position') || 0;

        // Modal'i kapat ve scroll'u geri yukle
        modal.hide();
        $('body').removeClass('modal-open');
        $('html').removeClass('modal-open');
        // CSS top'u kaldirma gerekmiyor artik
        $(window).scrollTop(scrollTop);
    }

    function clearFilters() {
        const form = $('#shop-filters-form');
        
        // Tum checkbox'lari temizle
        form.find('input[type="checkbox"]').prop('checked', false);
        
        // Fiyat inputlarini temizle
        form.find('input[type="number"]').val('');
        
        // Filtre sayisini guncelle
        updateFilterCount();
        
        // Filtreleri uygula (temiz hali)
        applyFilters();
    }

    function applyFilters() {
        const form = $('#shop-filters-form');
        const params = new URLSearchParams();

        // Mevcut URL parametrelerini al
        const currentParams = new URLSearchParams(window.location.search);

        // Sayfa numarasini sifirla (filtreleme sonrasi ilk sayfaya don)
        currentParams.delete('paged');

        // Fiyat filtrelerini ekle
        const minPrice = form.find('input[name="min_price"]').val();
        const maxPrice = form.find('input[name="max_price"]').val();

        if (minPrice && minPrice.trim() !== '') {
            params.set('min_price', minPrice);
        }

        if (maxPrice && maxPrice.trim() !== '') {
            params.set('max_price', maxPrice);
        }

        // Checkbox array'lerini isle
        const checkboxGroups = ['product_cat', 'product_tag', 'stock_status'];
        checkboxGroups.forEach(function(group) {
            const checkedValues = [];
            form.find('input[name="' + group + '[]"]:checked').each(function() {
                checkedValues.push($(this).val());
            });

            if (checkedValues.length > 0) {
                params.set(group, checkedValues.join(','));
            }
        });

        // Diger mevcut parametreleri koru (arama, kategori vb.)
        for (let [key, value] of currentParams.entries()) {
            if (!['min_price', 'max_price', 'product_cat', 'product_tag', 'stock_status', 'paged'].includes(key)) {
                params.set(key, value);
            }
        }

        // Yeni URL olustur ve yonlendir
        let newUrl = window.location.pathname;
        if (params.toString()) {
            newUrl += '?' + params.toString();
        }

        // Loading goster
        showLoading();

        // Modal'i kapat
        closeModal();

        // Sayfayi yenile
        window.location.href = newUrl;
    }

    function updateFilterCount() {
        const form = $('#shop-filters-form');
        let count = 0;

        // Checkbox'lari say
        count += form.find('input[type="checkbox"]:checked').length;

        // Fiyat inputlarini kontrol et
        const minPrice = form.find('input[name="min_price"]').val();
        const maxPrice = form.find('input[name="max_price"]').val();

        if (minPrice && minPrice.trim() !== '') count++;
        if (maxPrice && maxPrice.trim() !== '') count++;

        // Eger form henuz yuklenmediyse, URL parametrelerinden say
        if (form.length === 0 || form.find('input').length === 0) {
            count = getFilterCountFromURL();
        }

        // Buton metnini guncelle - tum filtre butonlari icin
        const toggleBtn = $('.shop-filters-toggle'); // Class seçici kullan
        const originalText = 'Filtreler';

        if (count > 0) {
            toggleBtn.html('<i class="fas fa-filter"></i> ' + originalText + ' (' + count + ')');
            toggleBtn.addClass('has-filters');
        } else {
            toggleBtn.html('<i class="fas fa-filter"></i> ' + originalText);
            toggleBtn.removeClass('has-filters');
        }
    }

    function getFilterCountFromURL() {
        const urlParams = new URLSearchParams(window.location.search);
        let count = 0;

        // Kategori filtrelerini say
        const productCat = urlParams.get('product_cat');
        if (productCat) {
            count += productCat.split(',').length;
        }

        // Etiket filtrelerini say
        const productTag = urlParams.get('product_tag');
        if (productTag) {
            count += productTag.split(',').length;
        }

        // Stok durumu filtrelerini say
        const stockStatus = urlParams.get('stock_status');
        if (stockStatus) {
            count += stockStatus.split(',').length;
        }

        // Fiyat filtrelerini say
        if (urlParams.get('min_price')) count++;
        if (urlParams.get('max_price')) count++;

        return count;
    }

    function showLoading() {
        const toggleBtn = $('.shop-filters-toggle'); // Class seçici kullan
        toggleBtn.html('<i class="fas fa-spinner fa-spin"></i> Yukleniyor...');
        toggleBtn.prop('disabled', true);
    }



})(jQuery);

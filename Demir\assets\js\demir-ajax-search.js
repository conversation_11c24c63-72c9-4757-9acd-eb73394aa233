/**
 * Demir <PERSON>X Search Functionality
 * 
 * @package demir
 */

(function() {
    'use strict';

    // Configuration
    const config = {
        minCharacters: 2,
        searchDelay: 300,
        maxResults: 8
    };

    let searchTimeout;
    let currentRequest;

    /**
     * Initialize AJAX search functionality
     */
    function initAjaxSearch() {
        const searchForms = document.querySelectorAll('.demir-ajax-search-form');
        
        searchForms.forEach(function(form) {
            const searchInput = form.querySelector('.search-field');
            const resultsContainer = form.querySelector('.demir-search-results');
            
            if (!searchInput || !resultsContainer) return;

            // Add event listeners
            searchInput.addEventListener('input', function(e) {
                handleSearchInput(e, resultsContainer, form);
            });

            searchInput.addEventListener('focus', function(e) {
                if (resultsContainer.innerHTML.trim() !== '') {
                    resultsContainer.style.display = 'block';
                }
            });

            searchInput.addEventListener('blur', function(e) {
                // Delay hiding to allow clicking on results
                setTimeout(function() {
                    resultsContainer.style.display = 'none';
                }, 200);
            });

            // Prevent form submission if AJAX is active
            form.addEventListener('submit', function(e) {
                if (resultsContainer.style.display === 'block' && resultsContainer.innerHTML.trim() !== '') {
                    e.preventDefault();
                }
            });
        });
    }

    /**
     * Handle search input events
     */
    function handleSearchInput(event, resultsContainer, form) {
        const query = event.target.value.trim();
        
        // Clear previous timeout
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }

        // Cancel previous request
        if (currentRequest) {
            currentRequest.abort();
        }

        // Hide results if query is too short
        if (query.length < config.minCharacters) {
            resultsContainer.style.display = 'none';
            resultsContainer.innerHTML = '';
            return;
        }

        // Set new timeout for search
        searchTimeout = setTimeout(function() {
            performSearch(query, resultsContainer, form);
        }, config.searchDelay);
    }

    /**
     * Perform AJAX search
     */
    function performSearch(query, resultsContainer, form) {
        const nonce = form.querySelector('input[name="demir_nonce"]').value;
        const ajaxUrl = getAjaxUrl();
        
        const url = ajaxUrl + '?demir-ajax=demir_ajax_search&query=' + 
                   encodeURIComponent(query) + '&demir_nonce=' + nonce;

        // Show loading state
        showLoadingState(resultsContainer);

        // Make AJAX request
        currentRequest = fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                displayResults(data, resultsContainer);
            })
            .catch(error => {
                if (error.name !== 'AbortError') {
                    console.error('Search error:', error);
                    hideResults(resultsContainer);
                }
            });
    }

    /**
     * Get AJAX URL
     */
    function getAjaxUrl() {
        // Try to get from localized script first
        if (typeof demirAjaxSearch !== 'undefined' && demirAjaxSearch.ajaxUrl) {
            return demirAjaxSearch.ajaxUrl.replace('admin-ajax.php', '');
        }

        // Try to get from WordPress AJAX URL
        if (typeof ajaxurl !== 'undefined') {
            return ajaxurl.replace('admin-ajax.php', '');
        }

        // Fallback to current site URL
        return window.location.origin + window.location.pathname;
    }

    /**
     * Show loading state
     */
    function showLoadingState(resultsContainer) {
        resultsContainer.innerHTML = '<div class="demir-search-loading">Aranıyor...</div>';
        resultsContainer.style.display = 'block';
    }

    /**
     * Display search results
     */
    function displayResults(data, resultsContainer) {
        if (!data.suggestions || data.suggestions.length === 0) {
            resultsContainer.innerHTML = '<div class="demir-search-no-results">Sonuç bulunamadı</div>';
            resultsContainer.style.display = 'block';
            return;
        }

        let html = '<div class="demir-search-results-list">';
        
        data.suggestions.forEach(function(item) {
            html += '<div class="demir-search-result-item">';
            html += '<a href="' + item.url + '" class="demir-search-result-link">';
            
            if (item.image) {
                html += '<div class="demir-search-result-image">';
                html += '<img src="' + item.image + '" alt="' + item.title + '" loading="lazy">';
                html += '</div>';
            }
            
            html += '<div class="demir-search-result-content">';
            html += '<h4 class="demir-search-result-title">' + item.title + '</h4>';
            
            if (item.price) {
                html += '<div class="demir-search-result-price">' + item.price + '</div>';
            }
            
            if (item.description) {
                html += '<div class="demir-search-result-description">' + item.description + '</div>';
            }
            
            html += '</div>';
            html += '</a>';
            html += '</div>';
        });

        if (data.view_all_link && data.result_total > data.suggestions.length) {
            html += '<div class="demir-search-view-all">';
            html += '<a href="' + data.view_all_link + '" class="demir-search-view-all-link">';
            html += 'Tüm sonuçları gör (' + data.result_total + ')';
            html += '</a>';
            html += '</div>';
        }

        html += '</div>';
        
        resultsContainer.innerHTML = html;
        resultsContainer.style.display = 'block';
    }

    /**
     * Hide results
     */
    function hideResults(resultsContainer) {
        resultsContainer.style.display = 'none';
        resultsContainer.innerHTML = '';
    }

    /**
     * Initialize when DOM is ready
     */
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initAjaxSearch);
    } else {
        initAjaxSearch();
    }

})();

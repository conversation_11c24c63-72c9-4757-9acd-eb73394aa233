/**
 * /*!
 * Theme Name:      	Demir
 * Theme URI:       	https://www.example.com/wordpress-themes/demir
 * Author:          	Your Name
 * Author URI:      	https://www.example.com/
 * Description:     	Demir is the perfect theme for your next WooCommerce project designed around speed and conversions.
 * Version:         	1.0.0
 * Requires at least: 	5.6
 * Tested up to:        6.5
 * Requires PHP: 		7.3
 * License:         	GNU General Public License v2 or later
 * License URI:     	http://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain:     	demir
 * wc_gzd_compatible: 	true
 * Tags:            	e-commerce, two-columns, left-sidebar, right-sidebar, custom-background, custom-colors, custom-header, custom-menu, featured-images, full-width-template, threaded-comments, accessibility-ready, rtl-language-support, footer-widgets, sticky-post, theme-options
 *
 * This theme, like WordPress, is licensed under the GPL.
 * Use it to make something cool, have fun, and share what you've learned with others.
 *
 * Demir is based on Underscores http://underscores.me/, (C) 2012-2019 Automattic, Inc. and Storefront https://github.com/woocommerce/storefront (C) 2012-2019 Automattic, Inc.
 * Resetting and rebuilding styles have been helped along thanks to the fine work of
 * <PERSON> http://meyerweb.com/eric/tools/css/reset/index.html
 * along with <PERSON> and <PERSON> <PERSON> http://necolas.github.com/normalize.css/
 *
 * Demir documentation is available here: https://www.example.com/docs/demir-theme/
 *
 * Rivolicons License: Created by Hadrien Boyer and licensed under Creative Commons 4.0 - https://creativecommons.org/licenses/by-sa/4.0/
 * Rivolicons icon pack homepage - http://rivolicons.hadrien.co/
 * Images License: GNU General Public License v2 or later
 *
 * ======
 * Note: Do not edit this file. If you wish to add your own CSS we strongly recommend creating your own child theme, or installing a custom CSS plugin.
 * All main styles are in /assets/css/main/main.css
 * ======
 *
 */